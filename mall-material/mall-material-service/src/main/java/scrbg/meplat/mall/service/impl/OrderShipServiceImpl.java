package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import com.scrbg.common.utils.R;
import org.apache.commons.lang.StringUtils;
import org.redisson.Redisson;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.dto.order.OrderShipmentsQtyIsOkDTO;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.OrderShipMapper;
import scrbg.meplat.mall.mapper.ProductMapper;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.service.stockManage.SecondarySupplierLogService;
import scrbg.meplat.mall.service.stockManage.SelfOperatedLogService;
import scrbg.meplat.mall.util.*;
import scrbg.meplat.mall.util.countExcel.ExcelForWebUtil;
import scrbg.meplat.mall.vo.app.ShipFiles;
import scrbg.meplat.mall.vo.product.StationMessageReceiveVO;
import scrbg.meplat.mall.vo.ship.*;
import scrbg.meplat.mall.vo.shopManage.ReturnGoodItemVo;
import scrbg.meplat.mall.vo.shopManage.ReturnGoodsVo;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2023-05-22
 */
@Service
public class OrderShipServiceImpl extends ServiceImpl<OrderShipMapper, OrderShip> implements OrderShipService {
    @Autowired
    OrdersService ordersService;

    @Autowired
    ProductSkuService productSkuService;
    @Autowired
    RestTemplateUtils restTemplateUtils;

    @Autowired
    OrderItemService orderItemService;
    @Autowired
    ProductCategoryService productCategoryService;

    @Autowired
    MaterialMonthSupplyPlanDtlService materialMonthSupplyPlanDtlService;

    @Autowired
    InterfaceLogsService interfaceLogsService;

    @Autowired
    public MallConfig mallConfig;
    @Resource
    private Redisson redisson;

    @Autowired
    FileService fileService;
    @Autowired
    OrderShipDtlService orderShipDtlService;
    @Autowired
    OrderReturnService orderReturnService;
    @Autowired
    OrderReturnItemService orderReturnItemService;


    @Autowired
    private EnterpriseInfoService enterpriseInfoService;
    @Autowired
    OrderSelectPlanService orderSelectPlanService;
    @Autowired
    OutSupplierRecevieService outSupplierRecevieService;
    @Autowired
    ShopService shopService;

    @Autowired
    private SecondarySupplierLogService supplierLogService;

    @Autowired
    private SelfOperatedLogService selfOperatedLogService;

    @Autowired
    ProductMapper productMapper;

    @Autowired
    private StationMessageService stationMessageService;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<OrderShip> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        queryWrapper.eq(OrderShip::getShipEnterpriseId, user.getEnterpriseId());
        String abovePrice = (String) innerMap.get("abovePrice");
        String belowPrice = (String) innerMap.get("belowPrice");
        String orderId = (String) innerMap.get("orderId");
        Integer type = (Integer) innerMap.get("type");
        String orderSn = (String) innerMap.get("orderSn");
        Integer orderClass = (Integer) innerMap.get("orderClass");
        Integer sourceType = (Integer) innerMap.get("sourceType");
        String otherOrderSn = (String) innerMap.get("otherOrderSn");
        String staConfirmTime = (String) innerMap.get("staConfirmTime");
        String endConfirmTime = (String) innerMap.get("endConfirmTime");
        String startGmtCreate = (String) innerMap.get("startGmtCreate");
        String endGmtCreate = (String) innerMap.get("endGmtCreate");
        String staShipTime = (String) innerMap.get("staShipTime");
        String endShipTime = (String) innerMap.get("endShipTime");
        Integer orderBy = (Integer) innerMap.get("orderBy");
        String keywords = (String) innerMap.get("keywords");
        String receiveOrgName = (String) innerMap.get("receiveOrgName");
        Integer productType = (Integer) innerMap.get("productType");
        queryWrapper.eq(orderClass != null, OrderShip::getOrderClass, orderClass);
        if (productType != null) {
            queryWrapper.eq(OrderShip::getProductType, productType);
        }
        if (orderId != null && orderId != "") {
            queryWrapper.eq(OrderShip::getOrderId, orderId);
        }
        if (belowPrice != null && belowPrice != "") {
            queryWrapper.lt(OrderShip::getRateAmount, belowPrice);
        }

        if (abovePrice != null && abovePrice != "") {
            queryWrapper.gt(OrderShip::getRateAmount, abovePrice);
        }
        if (type != null) {
            queryWrapper.eq(OrderShip::getType, type);
        }
        queryWrapper.eq(sourceType != null, OrderShip::getSourceType, sourceType);
        if (StringUtils.isNotBlank(orderSn)) {
            queryWrapper.eq(OrderShip::getOrderSn, orderSn);
        }
        if (StringUtils.isNotBlank(otherOrderSn)) {
            queryWrapper.eq(OrderShip::getOtherOrderSn, otherOrderSn);
        }
        if (staConfirmTime != null && staConfirmTime != "") {
            queryWrapper.gt(OrderShip::getConfirmTime, staConfirmTime);
        }
        if (endConfirmTime != null && endConfirmTime != "") {
            queryWrapper.lt(OrderShip::getConfirmTime, endConfirmTime);
        }
        if (startGmtCreate != null && startGmtCreate != "") {
            queryWrapper.gt(OrderShip::getGmtCreate, startGmtCreate);
        }
        if (endGmtCreate != null && endGmtCreate != "") {
            queryWrapper.lt(OrderShip::getGmtCreate, endGmtCreate);
        }
        if (staShipTime != null && staShipTime != "") {
            queryWrapper.gt(OrderShip::getShipData, staShipTime);
        }
        if (endShipTime != null && endShipTime != "") {
            queryWrapper.lt(OrderShip::getShipData, endShipTime);
        }
        if (orderBy != null) {
            if (orderBy == 1) {
                queryWrapper.orderByDesc(OrderShip::getGmtCreate);
            }
            if (orderBy == 2) {
                queryWrapper.orderByDesc(OrderShip::getFlishTime);
            }
            if (orderBy == 3) {
                queryWrapper.orderByDesc(OrderShip::getShipData);
            }
        } else {
            queryWrapper.orderByDesc(OrderShip::getGmtCreate);
        }
        if (receiveOrgName != null && receiveOrgName != "") {
            queryWrapper.like(OrderShip::getReceiveOrgName, receiveOrgName);
        }
        if (keywords != null && keywords != "") {
            queryWrapper.and(i -> i.like(OrderShip::getReceiveName, keywords)
                    .or().like(OrderShip::getReceiveOrgName, keywords)
                    .or().like(OrderShip::getBillSn, keywords)
                    .or().like(OrderShip::getOrderSn, keywords));
        }
        IPage<OrderShip> page = page(new Query<OrderShip>().getPage(jsonObject), queryWrapper);
        return new PageUtils(page);
    }


    @Override
    public PageUtils queryPageTwo(JSONObject jsonObject, LambdaQueryWrapper<OrderShip> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        queryWrapper.eq(OrderShip::getSupplierId, user.getEnterpriseId());
        getWrapper(queryWrapper, innerMap);

        IPage<OrderShip> page = page(new Query<OrderShip>().getPage(jsonObject), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changShipNum(List<SubmitOrderShipDtl> vos) {
        for (SubmitOrderShipDtl vo : vos) {
            if (vo.getShipNum().compareTo(BigDecimal.valueOf(0)) < 0) {
                throw new BusinessException(500, "发货单项" + vo.getDtlId() + "确认收货数量不能为负数");
            }
            OrderShipDtl dtl = orderShipDtlService.getById(vo.getDtlId());
            if (dtl != null) {
                if (vo.getShipNum().compareTo(dtl.getShipCounts()) > 0) {
                    throw new BusinessException(500, "发货单" + dtl.getDtlId() + "确认收货数量不能大于发货数量");
                } else if (vo.getShipNum().compareTo(dtl.getShipCounts()) == 0) {
                    dtl.setShipNum(vo.getShipNum());
                    dtl.setIsEqual(0);
                } else {
                    dtl.setShipNum(vo.getShipNum());
                    dtl.setIsEqual(1);
                }
                orderShipDtlService.updateById(dtl);
            }


        }


    }

    private void getWrapper(LambdaQueryWrapper<OrderShip> queryWrapper, Map<String, Object> innerMap) {
        String abovePrice = (String) innerMap.get("abovePrice");
        String belowPrice = (String) innerMap.get("belowPrice");
        String orderId = (String) innerMap.get("orderId");
        Integer type = (Integer) innerMap.get("type");
        String orderSn = (String) innerMap.get("orderSn");
        String staConfirmTime = (String) innerMap.get("staConfirmTime");
        String endConfirmTime = (String) innerMap.get("endConfirmTime");
        String startGmtCreate = (String) innerMap.get("startGmtCreate");
        String endGmtCreate = (String) innerMap.get("endGmtCreate");
        Integer orderClass = (Integer) innerMap.get("orderClass");
        String staShipTime = (String) innerMap.get("staShipTime");
        String endShipTime = (String) innerMap.get("endShipTime");
        Integer orderBy = (Integer) innerMap.get("orderBy");
        String keywords = (String) innerMap.get("keywords");
        String receiveOrgName = (String) innerMap.get("receiveOrgName");
        Integer productType = (Integer) innerMap.get("productType");

        if (productType != null) {
            queryWrapper.eq(OrderShip::getProductType, productType);
        }
        if (orderId != null && orderId != "") {
            queryWrapper.eq(OrderShip::getOrderId, orderId);
        }
        if (belowPrice != null && belowPrice != "") {
            queryWrapper.lt(OrderShip::getRateAmount, belowPrice);
        }

        if (abovePrice != null && abovePrice != "") {
            queryWrapper.gt(OrderShip::getRateAmount, abovePrice);
        }
        if (type != null) {
            queryWrapper.eq(OrderShip::getType, type);
        }
        if (orderSn != null && orderSn != "") {
            queryWrapper.eq(OrderShip::getOrderSn, orderSn);
        }
        if (staConfirmTime != null && staConfirmTime != "") {
            queryWrapper.gt(OrderShip::getConfirmTime, staConfirmTime);
        }
        if (endConfirmTime != null && endConfirmTime != "") {
            queryWrapper.lt(OrderShip::getConfirmTime, endConfirmTime);
        }
        if (startGmtCreate != null && startGmtCreate != "") {
            queryWrapper.gt(OrderShip::getGmtCreate, startGmtCreate);
        }
        if (endGmtCreate != null && endGmtCreate != "") {
            queryWrapper.lt(OrderShip::getGmtCreate, endGmtCreate);
        }
        if (staShipTime != null && staShipTime != "") {
            queryWrapper.gt(OrderShip::getShipData, staShipTime);
        }
        if (endShipTime != null && endShipTime != "") {
            queryWrapper.lt(OrderShip::getShipData, endShipTime);
        }
        if (orderBy != null) {
            if (orderBy == 1) {
                queryWrapper.orderByDesc(OrderShip::getGmtCreate);
            }
            if (orderBy == 2) {
                queryWrapper.orderByDesc(OrderShip::getFlishTime);
            }
            if (orderBy == 3) {
                queryWrapper.orderByDesc(OrderShip::getShipData);
            }
        } else {
            queryWrapper.orderByDesc(OrderShip::getShipData);
        }
        if (receiveOrgName != null && receiveOrgName != "") {
            queryWrapper.like(OrderShip::getReceiveOrgName, receiveOrgName);
        }
        if (keywords != null && keywords != "") {
            queryWrapper.and(i -> i.like(OrderShip::getReceiveName, keywords).or()
                    .like(OrderShip::getReceiveOrgName, keywords)
                    .or().like(OrderShip::getOrderSn, keywords));
        }


        if (orderClass != null) {
            queryWrapper.eq(OrderShip::getOrderClass, orderClass);
        }
//        else {
//            queryWrapper.in(OrderShip::getOrderClass, 1, 2);
//        }
    }

    @Override
    public void create(OrderShip orderShip) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(orderShip);
    }

    @Override
    public void update(OrderShip orderShip) {
        super.updateById(orderShip);
    }


    @Override
    public OrderShip getById(String id) {
        OrderShip byId = baseMapper.selectById(id);
        List<File> files = fileService.listReIdAndTypeAndPKey(id, 10, null);
        if (files != null && files.size() > 0) {
            byId.setFiles(files);
        } else {
            byId.setFiles(new ArrayList<>());
        }
        return byId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        if (id == null) {
            throw new BusinessException(601, "发货单号为空");
        }
        OrderShip byId = getById(id);

        if (byId != null) {
            if (byId.getType() == 0) {
                List<OrderShipDtl> dtls = orderShipDtlService.getDataByOrderShipId(id);
                for (OrderShipDtl dtl : dtls) {
                    OrderItem orderItem = orderItemService.getById(dtl.getOrderItemId());
                    orderItem.setShipCounts(orderItem.getShipCounts().subtract(dtl.getShipCounts()));
//                    if (orderItem.getParentOrderItemId()!=null){
//                        OrderItem parent = orderItemService.getById(orderItem.getParentOrderItemId());
//                        parent.setShipCounts( orderItem.getShipCounts());
//                        orderItemService.updateShipCounts(parent);
//                    }

                    orderShipDtlService.removeById(dtl.getDtlId());
                    orderItemService.updateShipCounts(orderItem);
                }
                super.removeById(id);

            } else {
                throw new BusinessException(603, "发货单已确认，不能删除");
            }

        } else {
            throw new BusinessException(602, "发货单不存在");
        }

    }

    /**
     * 根据订单项生成发货单
     *
     * @param orderShipVo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrderShip(OrderShipVo orderShipVo) {
        if (orderShipVo == null || orderShipVo.getDtls() == null || orderShipVo.getDtls().size() == 0) {
            throw new BusinessException(500, "请输入发货单或者填写发货单项");
        }
        OrderShip orderShip = new OrderShip();
        Orders orders = ordersService.getById(orderShipVo.getOrderId());


        //根据订单生成发货单，保存数据库
        if (orders.getParentOrderId() != null) {
            Orders one = ordersService.getById(orders.getParentOrderId());
            orderShipVo.setOrderClass(3);
            orderShip = saveShipInfo(orderShipVo, one);
            orderShip.setOtherOrderId(orders.getOrderId());
            orderShip.setOtherOrderSn(orders.getOrderSn());

        } else {
            orderShip = saveShipInfo(orderShipVo, orders);
        }
        //判断是否有二级订单

        BigDecimal rateAmous = BigDecimal.valueOf(0);
        BigDecimal noRateAmous = BigDecimal.valueOf(0);
        BigDecimal otherRateAmous = BigDecimal.valueOf(0);
        BigDecimal otherNoRateAmous = BigDecimal.valueOf(0);
        List<OrderShipDtl> dtls = orderShip.getDtls();
        //判断传递的发货单项的数量是否全部为0
        int dtlcounts = 0;
        //根据订单项和来源创建发货单明细
        ArrayList<OrderShipmentsQtyIsOkDTO> list = new ArrayList<>();
        for (OrderShipDtl dtl : dtls) {
//            dtl.setShipNum(dtl.getShipCounts());
            if (dtl.getShipCounts().compareTo(BigDecimal.valueOf(0)) == 0) {
                dtlcounts++;
                continue;
            } else {
                dtl.setBillId(orderShip.getBillId());
                dtl.setBillSn(orderShip.getBillSn());
                dtl.setOrderId(orderShip.getOrderId());
                dtl.setOrderSn(orderShip.getOrderSn());
                //根据订单项生成发货单项，并修改订单发货数量（原有数量+本次发货数量）
                createOrderShipDtl(dtl, orderShip.getSourceType(), list);
                rateAmous = rateAmous.add(dtl.getTotalAmount());
                noRateAmous = noRateAmous.add(dtl.getNoRateAmount());
                if (orders.getOrderClass() != 1) {
                    otherRateAmous = otherRateAmous.add(dtl.getOtherTotalAmount());
                    otherNoRateAmous = otherNoRateAmous.add(dtl.getOtherNoRateAmount());
                }
                if (orderShip.getSourceType() == 1) {
                    orderItemService.orderShipmentsQtyIsOkYG(list);
                } else if (orderShip.getSourceType() == 6) {
                    orderItemService.orderShipmentsQtyIsOkLG(list);
                }

            }
        }
        if (dtls.size() == dtlcounts) {
            throw new BusinessException(500, "请填写发货数量");
        }
        orderShip.setRateAmount(rateAmous);
        orderShip.setNoRateAmount(noRateAmous);
        orderShip.setOtherRateAmount(otherRateAmous);
        orderShip.setOtherNoRateAmount(otherNoRateAmous);
        update(orderShip);
        orderShipDtlService.saveBatch(dtls);
    }

    private void createOrderShipDtl(OrderShipDtl dtl, Integer sourType, ArrayList<OrderShipmentsQtyIsOkDTO> list) {
        //获取主订单
        OrderItem orderItem = orderItemService.getById(dtl.getOrderItemId());
        //获得发货数量
        BigDecimal newShipCount = orderItem.getShipCounts().add(dtl.getShipCounts());
        if (orderItem.getParentOrderItemId() != null) {
            orderItem = orderItemService.getById(orderItem.getParentOrderItemId());
        }
        /**
         * 设置发货单项
         */
        dtl.setProductId(orderItem.getProductId());
        dtl.setTotalSum(orderItem.getBuyCounts());
        dtl.setProductName(orderItem.getProductName());
        dtl.setProductSn(orderItem.getProductSn());
        dtl.setSkuName(orderItem.getSkuName());
        dtl.setTexture(orderItem.getTexture());
        dtl.setUnit(orderItem.getUnit());
        /**
         * 查询商城退货数量
         */
        BigDecimal count = orderItem.getReturnCounts();
        BigDecimal pcwpReturns = orderItem.getPcwpReturn();
        //2 计划
        if (sourType == 2) {
            //可发货数量= 购买数量-（订单发货数量+商城退货数量）
            if (dtl.getShipCounts().compareTo(orderItem.getBuyCounts().subtract(orderItem.getShipCounts().add(count).subtract(pcwpReturns))) <= 0) {
                //设置发货单的含税和不含税金额，修改订单项的发货数量
                setOrderShipCounts(dtl, orderItem);
            } else {
                throw new BusinessException(500, "发货+商城退货数量大于购买数量，不能生成发货单");
            }
        } else {
            OrderShipmentsQtyIsOkDTO orderShipmentsQtyIsOkDTO = new OrderShipmentsQtyIsOkDTO();
            BigDecimal aboveQty = orderItem.getBuyCounts().add(orderItem.getBuyCounts().multiply(BigDecimal.valueOf(0.1)));
            if (dtl.getShipCounts().compareTo(aboveQty.subtract(orderItem.getShipCounts().add(count).subtract(pcwpReturns))) <= 0) {
                if (sourType == 1) {
                    //设置发货单的含税和不含税金额，修改订单项的发货数量
                    setMonthOrderShipCounts(dtl, orderItem);
                } else if (sourType == 6) {
                    //设置发货单的含税和不含税金额，修改订单项的发货数量
                    setOrderShipCounts(dtl, orderItem);
                }
                orderShipmentsQtyIsOkDTO.setOrderItemId(dtl.getOrderItemId());
                orderShipmentsQtyIsOkDTO.setQty(dtl.getShipCounts());
                list.add(orderShipmentsQtyIsOkDTO);
            } else {
                throw new BusinessException(500, "发货+商城退货数量大于超量（购买数量+10%），不能生成发货单");
            }
        }


//        /**
//         *
//         * 2，3  大宗临购和零星采购有金额
//         * 零星采购发货单项根据发货数量后确认发货数量生成 金额 和单价
//         * @param orderItem   订单项
//         * @param dtl    发货单
//         * @param count  发货数量后确认发货数量
//         */
//        orderShipDtlService.updateAmount(orderItem, dtl, dtl.getShipCounts());

    }


    //1  合同  没有金额
    //月供，不计算价格,默认价格为o
    private void setMonthOrderShipCounts(OrderShipDtl dtl, OrderItem main0rderItem) {
        BigDecimal count = main0rderItem.getShipCounts().add(dtl.getShipCounts());
        dtl.setOrderSn(main0rderItem.getOrderSn());
        dtl.setOrderItemId(main0rderItem.getOrderItemId());
        dtl.setOrderId(main0rderItem.getOrderId());
        dtl.setProductPrice(BigDecimal.ZERO);
        dtl.setTotalAmount(BigDecimal.ZERO);
        dtl.setNoRatePrice(BigDecimal.ZERO);
        dtl.setNoRateAmount(BigDecimal.ZERO);
        dtl.setNoRateAmount(BigDecimal.ZERO);
        //二级修改数量
        OrderItem son = orderItemService.getOrderItemByParentId(main0rderItem.getOrderItemId());
        if (son != null) {
            dtl.setOtherTotalAmount(BigDecimal.ZERO);
            dtl.setOtherNoRateAmount(BigDecimal.ZERO);
            dtl.setOtherNoRatePrice(BigDecimal.ZERO);
            dtl.setOtherProductPrice(BigDecimal.ZERO);
            son.setShipCounts(count);
            orderItemService.updateById(son);
        }
        main0rderItem.setShipCounts(count);
        orderItemService.updateById(main0rderItem);

    }


    /**
     * orderItem 为主订单
     *
     * @param dtl
     * @param main0rderItem
     */
    private void setOrderShipCounts(OrderShipDtl dtl, OrderItem main0rderItem) {
        //现在发货单数量=原有数量+现在发货数量
        BigDecimal count = main0rderItem.getShipCounts().add(dtl.getShipCounts());
        dtl.setOrderSn(main0rderItem.getOrderSn());
        dtl.setOrderItemId(main0rderItem.getOrderItemId());
        dtl.setOrderId(main0rderItem.getOrderId());
        //计算发货单项的含税和不含税价格
        dtl.setProductPrice(main0rderItem.getProductPrice());
        //含税价格=订单项单价  *  发货单数量
        dtl.setTotalAmount(main0rderItem.getProductPrice().multiply(dtl.getShipCounts()));
        dtl.setTaxRate(main0rderItem.getTaxRate());
        BigDecimal notRatePrice = TaxCalculator.calculateNotTarRateAmount(main0rderItem.getProductPrice(), main0rderItem.getTaxRate());

        BigDecimal notRateAmount = TaxCalculator.noTarRateItemAmount(dtl.getTotalAmount(), notRatePrice, dtl.getShipCounts(), main0rderItem.getTaxRate());
        dtl.setNoRatePrice(notRatePrice);
        dtl.setNoRateAmount(notRateAmount);

        //二级修改数量
        OrderItem son = orderItemService.getOrderItemByParentId(main0rderItem.getOrderItemId());
        if (main0rderItem.getParentOrderItemId() == null) {
            if (son != null) {
                son.setShipCounts(count);
                dtl.setOtherTotalAmount(son.getProductPrice().multiply(dtl.getShipCounts()));
                dtl.setOtherProductPrice(son.getProductPrice());
                dtl.setOtherTaxRate(son.getTaxRate());
                BigDecimal otherNoRatePrice = TaxCalculator.calculateNotTarRateAmount(son.getProductPrice(), son.getTaxRate());
                BigDecimal otherNoRateAmount = TaxCalculator.noTarRateItemAmount(dtl.getOtherTotalAmount(), otherNoRatePrice, dtl.getShipCounts(), son.getTaxRate());
                dtl.setOtherNoRateAmount(otherNoRateAmount);
                dtl.setOtherNoRatePrice(otherNoRatePrice);
                orderItemService.updateById(son);
            } else {
                OrderItem parent = orderItemService.getById(main0rderItem.getParentOrderItemId());
                if (parent != null) {
                    parent.setShipCounts(count);
                    orderItemService.updateById(parent);
                }
            }
        }
        main0rderItem.setShipCounts(count);
        orderItemService.updateById(main0rderItem);
    }

    //根据订单保存发货单信息
    private OrderShip saveShipInfo(OrderShipVo orderShip, Orders orders) {


        OrderSelectPlan plan = null;
        orderShip.setType(0);
        //设置发货单编号
        orderShip.setBillSn(OrderUtils.getOrder());
        //发货单需要使用主订单的订单id和编号 主订单关联的计划或合同
        if (orders.getOrderClass() == 1 || orders.getOrderClass() == 2) {
            plan = orderSelectPlanService.getDataByOrderSn(orders.getOrderSn());
            saveorderPlan(orderShip, orders, plan);
        } else {

            Orders parent = ordersService.getById(orders.getParentOrderId());
            plan = orderSelectPlanService.getDataByOrderSn(parent.getOrderSn());
            orderShip.setOtherOrderId(orders.getOrderId());
            orderShip.setOtherOrderSn(orders.getOrderSn());
            orders.setOrderClass(3);
            saveorderPlan(orderShip, parent, plan);
        }
        /**
         * 设置供应商信息
         */
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        orderShip.setShipUserName(user.getUserName());
        orderShip.setShipUserId(user.getUserId());
        orderShip.setShipEnterpriseId(user.getEnterpriseId());
        orderShip.setShipEnterpriseName(user.getEnterpriseName());
        orderShip.setSupplierOrgId(user.getOrgId());
        orderShip.setSupplierCode(user.getSocialCreditCode());


        /**
         * 设置采购员信息
         */
        orderShip.setEnterpriseId(orders.getEnterpriseId());
        orderShip.setEnterpriseName(orders.getEnterpriseName());
        orderShip.setReceivePhone(orders.getReceiverMobile());
        orderShip.setShipAddress(orders.getReceiverAddress());
        orderShip.setOrgId(orders.getOrgId());
        orderShip.setReceiveOrgName(orders.getEnterpriseName());


        orderShip.setFlishTime(orders.getFlishTime());
        orderShip.setOrderClass(orders.getOrderClass());
        OrderShip orderShip1 = new OrderShip();
        BeanUtils.copyProperties(orderShip, orderShip1);
        Integer count = ordersService.lambdaQuery().eq(Orders::getParentOrderId, orders.getOrderId()).count();
        if (count > 0) {
            orderShip1.setOrderClass(3);
        }
        save(orderShip1);
        return orderShip1;
    }

    private OrderShip outSaveShipInfo(OrderShipVo orderShip, Orders orders) {


        OrderSelectPlan plan = null;
        orderShip.setType(0);
        //设置发货单编号
        orderShip.setBillSn(OrderUtils.getOrder());
        //发货单需要使用主订单的订单id和编号 主订单关联的计划或合同
        if (orders.getOrderClass() == 1 || orders.getOrderClass() == 2) {
            plan = orderSelectPlanService.getDataByOrderSn(orders.getOrderSn());
            saveorderPlan(orderShip, orders, plan);
        } else {
            Orders parent = ordersService.getById(orders.getParentOrderId());
            plan = orderSelectPlanService.getDataByOrderSn(parent.getOrderSn());
            orderShip.setOtherOrderId(orders.getOrderId());
            orderShip.setOtherOrderSn(orders.getOrderSn());
            orders.setOrderClass(3);
            saveorderPlan(orderShip, parent, plan);
        }
        /**
         * 设置采购员信息
         */
        orderShip.setEnterpriseId(orders.getEnterpriseId());
        orderShip.setEnterpriseName(orders.getEnterpriseName());
        orderShip.setReceivePhone(orders.getReceiverMobile());
        orderShip.setShipAddress(orders.getReceiverAddress());
        orderShip.setOrgId(orders.getOrgId());
        orderShip.setReceiveOrgName(orders.getEnterpriseName());


        orderShip.setFlishTime(orders.getFlishTime());
        orderShip.setOrderClass(orders.getOrderClass());
        OrderShip orderShip1 = new OrderShip();
        BeanUtils.copyProperties(orderShip, orderShip1);
        Integer count = ordersService.lambdaQuery().eq(Orders::getParentOrderId, orders.getOrderId()).count();
        if (count > 0) {
            orderShip1.setOrderClass(3);
        }
        save(orderShip1);
        return orderShip1;
    }

    private void saveorderPlan(OrderShipVo orderShip, Orders orders, OrderSelectPlan plan) {
        orderShip.setOrderSn(orders.getOrderSn());
        orderShip.setOrderId(orders.getOrderId());
        orderShip.setBillType(orders.getBillType());
        orderShip.setSupplierName(orders.getSupplierName());
        orderShip.setSupplierId(orders.getSupplierId());
        orderShip.setShopName(orders.getShopName());
        orderShip.setShopId(orders.getShopId());
        /**
         * 零星采购 计划编号 类型 2
         * 大宗材料 合同编号 类型2
         */
        if (plan != null) {
            switch (orders.getProductType()) {
                case 10:
                    orderShip.setSourceId(plan.getBillId());
                    orderShip.setSourceNo(plan.getBillNo());
                    orderShip.setProductType(10);
                    orderShip.setSourceType(2);
                    break;
                case 12:
                    orderShip.setSourceId(plan.getContractId());
                    orderShip.setSourceNo(plan.getContractNo());
                    orderShip.setProductType(12);
                    orderShip.setSourceType(1);
                    break;
                case 13:
                    orderShip.setSourceId(plan.getBillId());
                    orderShip.setSourceNo(plan.getBillNo());
                    orderShip.setProductType(13);
                    orderShip.setSourceType(6);
                    break;
                default:
                    break;
            }
        } else {
            throw new BusinessException(500, "订单没有关联计划");
        }
    }

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirmShip(SubmitOrderShipVo vo, String idStr, StringBuilder stringBuilder) {
        //            //2024-6-18 pcwp反馈发货单有重复提交数据问题，使用redis加锁缓存解决，时间设置为20秒过期
        //使用redis设置分布式锁
        String clientId = UUID.randomUUID().toString();
        Boolean b = stringRedisTemplate.opsForValue().setIfAbsent(MaterialLockUtils.SHIPBillNO_LOCK + vo.getBillId(), clientId, 2, TimeUnit.MINUTES);
        try {
            if (b) {
                OrderShip ship = getById(vo.getBillId());
                UserLogin user = ThreadLocalUtil.getCurrentUser();
                ship.setReceiveId(user.getFarUserId());
                ship.setReceiveName(user.getUserName());
                ship.setReceiveOrgName(user.getEnterpriseName());
                ship.setReceiveOrgId(user.getOrgId());
                ship.setConfirmTime(new Date());
                if (ship.getType() == 1) {
                    ship.setType(2);
                    update(ship);
                    BigDecimal totalAmounts = BigDecimal.valueOf(0);
                    BigDecimal noRateAmounts = BigDecimal.valueOf(0);
                    BigDecimal otherRateAmounts = BigDecimal.valueOf(0);
                    BigDecimal otherNoRateAmounts = BigDecimal.valueOf(0);
                    //查询发货单的所有发货项
                    List<SubmitOrderShipDtl> dtls = vo.getDtls();
                    if (dtls == null || dtls.size() <= 0) {
                        throw new BusinessException(500, "发货单项不能为空");
                    }
                    if (dtls != null && dtls.size() > 0) {
                        ArrayList<OrderShipDtl> shipDtls = new ArrayList<>();
                        int dtlsCount = 0;
                        for (SubmitOrderShipDtl dtl : dtls) {

                            if (dtl.getShipNum().compareTo(BigDecimal.valueOf(0)) <= 0) {
                                throw new BusinessException(500, "发货单项" + dtl.getDtlId() + "确认收货数量不能为0");
                            }
                            if (dtl != null) {
                                OrderShipDtl byId = orderShipDtlService.getById(dtl.getDtlId());
                                byId.setShipNum(dtl.getShipNum());
                                BigDecimal shipCounts = byId.getShipCounts();
                                byId.setShipCounts(dtl.getShipNum());
                                if (dtl.getShipNum().compareTo(byId.getShipCounts()) > 0) {
                                    throw new BusinessException(500, "发货单" + byId.getDtlId() + "确认收货数量不能大于发货数量");
                                }
                                OrderItem orderItem = orderItemService.getById(byId.getOrderItemId());
                                if (orderItem == null) {
                                    throw new BusinessException(500, "订单项不存在，请联系管理员");
                                } else {
                                    orderItem.setConfirmCounts(orderItem.getConfirmCounts().add(dtl.getShipNum()));
                                    orderItem.setShipCounts(orderItem.getShipCounts().subtract(shipCounts.subtract(dtl.getShipNum())));
                                    if (StringUtils.isNotBlank(orderItem.getParentOrderItemId())) {
                                        OrderItem parentOrderItem = orderItemService.getById(orderItem.getParentOrderItemId());
                                        parentOrderItem.setConfirmCounts(orderItem.getConfirmCounts());
                                        parentOrderItem.setShipCounts(orderItem.getShipCounts());
                                        orderItemService.update(parentOrderItem);
                                    } else {
                                        OrderItem son = orderItemService.getOrderItemByParentId(orderItem.getOrderItemId());
                                        if (son != null) {
                                            son.setConfirmCounts(orderItem.getConfirmCounts());
                                            son.setShipCounts(orderItem.getShipCounts());
                                            orderItemService.update(son);
                                        }
                                    }
                                    orderItemService.update(orderItem);
                                }
                                if (dtl.getShipNum().compareTo(byId.getShipCounts()) == 0) {
                                    dtlsCount++;
                                    byId.setIsEqual(0);
                                } else {
                                    dtlsCount++;
                                    byId.setIsEqual(1);
                                }
                                byId.setShipNum(dtl.getShipNum());
                                if (ship.getSourceType() != 1) {
                                    //发货单项价格
                                    byId.setTotalAmount(dtl.getShipNum().multiply(byId.getProductPrice()));
                                    BigDecimal productNoPrice = TaxCalculator.calculateNotTarRateAmount(byId.getProductPrice(), byId.getTaxRate());
                                    byId.setOtherTotalAmount(dtl.getShipNum().multiply(byId.getOtherProductPrice()));
                                    BigDecimal otherProductNoPrice = TaxCalculator.calculateNotTarRateAmount(byId.getOtherProductPrice(), byId.getTaxRate());
                                    BigDecimal notTarRateAmount = TaxCalculator.noTarRateItemAmount(byId.getTotalAmount(), productNoPrice, dtl.getShipNum(), byId.getTaxRate());
                                    BigDecimal notOtherTarRateAmount = TaxCalculator.noTarRateItemAmount(byId.getOtherTotalAmount(), otherProductNoPrice, dtl.getShipNum(), byId.getTaxRate());

                                    byId.setNoRateAmount(notTarRateAmount);
                                    byId.setOtherNoRateAmount(notOtherTarRateAmount);
                                    //发货单价格
                                    totalAmounts = totalAmounts.add(byId.getTotalAmount());
                                    noRateAmounts = noRateAmounts.add(notTarRateAmount);
                                    otherRateAmounts = otherRateAmounts.add(byId.getOtherTotalAmount());
                                    otherNoRateAmounts = otherNoRateAmounts.add(notOtherTarRateAmount);
                                }
                                shipDtls.add(byId);
                            } else {
                                throw new BusinessException(500, "发货单项" + dtl.getDtlId() + "不存在");
                            }
                        }
                        if (dtlsCount == 0) {
                            throw new BusinessException(500, "收料单确认收货不能为空，请输入确认收货数量");
                        }
                        ;

                        orderShipDtlService.updateBatchById(shipDtls);
                        ship.setDtls(shipDtls);
                    }
                    ship.setRateAmount(totalAmounts);
                    ship.setOtherRateAmount(otherRateAmounts);
                    ship.setOtherNoRateAmount(otherNoRateAmounts);
                    ship.setNoRateAmount(noRateAmounts);
                    //修改价格
                    update(ship);
                    if (0 == user.getIsInterior()) {
                        Boolean r11Bool = null;
                        //  判断是否能推送验收单
                        R r11 = null;
                        try {
                            String urlx = mallConfig.prodPcwp2Url02
                                    + PCWP2ApiUtil.IS_CON_OPERA_BILL_URL + "?orgId=" + ship.getOrgId() + "&date=" + DateUtil.getymd(LocalDate.now());
                            log.warn("判断是否可推送验收请求参数：" + urlx);
                            r11 = restTemplateUtils.getPCWP2NotParams(urlx);

                        } catch (Exception e) {
                            throw new BusinessException("【远程异常】判断是否可推送收料单：" + e.getMessage());
                        }
                        if (r11.getCode() == null || r11.getCode() != 200) {
                            // 返回不是200异常
                            throw new BusinessException("【远程异常】判断是否可推送收料单：" + r11.getMessage());
                        } else {
                            r11Bool = (Boolean) r11.getData();
                        }
                        log.warn("判断是否可推送验收返回：" + r11);
                        if (r11Bool == null || r11Bool == false) {
                            ship.setIsNotPush(1);
                            ship.setIdStr(idStr);
                            update(ship);
                            throw new BusinessException("pcwp月结中，请月结结束后在重新收货，参数orgid：" + ship.getOrgId() + "日期：" + DateUtil.getymd(LocalDate.now()));
                        } else {
                            update(ship);
                        }
                        //内部用户才会推送
                        outSupplierRecevieService.confirmShipUrl2(ship, idStr, stringBuilder);
                        //提醒收料员提交审核
                        remindAudit(user.getEnterpriseId());
                    }
                    ordersService.closeOrder2(ship.getOrderId());
                    //生成出入库记录
                    generateInOutStockRecord(ship);
                    //收料完成提醒
                    remindCompleted(ship.getOrderSn(),user);
                } else {
                    throw new BusinessException(1007, "收料推送重复");
                }
            } else {
                throw new BusinessException(1007, "收料推送重复");
            }
        } catch (
                BusinessException e) {
            throw new BusinessException(e.getMessage());
        } finally {
            if (clientId.equals(stringRedisTemplate.opsForValue().get(MaterialLockUtils.SHIPBillNO_LOCK + vo.getBillId()))) {
                stringRedisTemplate.delete(MaterialLockUtils.SHIPBillNO_LOCK + vo.getBillId());
            }
        }


    }

    private void generateInOutStockRecord(OrderShip ship) {
        if (null == ship) {
            return;
        }
        if (ship.getOrderClass() == 2) {

            List<SecondarySupplierRecord> records1 = new ArrayList<>();
            List<SelfOperatedStoreRecord> records2 = new ArrayList<>();
            ship.getDtls().forEach(dtl -> {
                //生成多供方出入库记录
                SecondarySupplierRecord inboundRecord1 = new SecondarySupplierRecord();
                assemblySupplierRecord(inboundRecord1, dtl, ship);
                inboundRecord1.setRecordType(1);
                SecondarySupplierRecord outboundRecord1 = new SecondarySupplierRecord();
                assemblySupplierRecord(outboundRecord1, dtl, ship);
                outboundRecord1.setRecordType(2);
                records1.add(inboundRecord1);
                records1.add(outboundRecord1);
                //生成自营店出入库记录
                SelfOperatedStoreRecord inboundRecord2 = new SelfOperatedStoreRecord();
                assemblySlefRecord(inboundRecord2, dtl, ship);
                SelfOperatedStoreRecord outboundRecord2 = new SelfOperatedStoreRecord();
                assemblySlefRecord(outboundRecord2, dtl, ship);
                records2.add(inboundRecord2);
                records2.add(outboundRecord2);
            });
            supplierLogService.saveLogs(records1);
            selfOperatedLogService.saveLogs(records2);
        }
    }

    private void assemblySlefRecord(SelfOperatedStoreRecord record, OrderShipDtl dtl, OrderShip ship) {
        Product product = productMapper.getByIdLocal(dtl.getProductId());
        OrderItem item = orderItemService.getById(dtl.getOrderItemId());
        if (null != product) {
            List<ProductSku> skuList = productSkuService.lambdaQuery()
                    .eq(ProductSku::getProductId, dtl.getProductId())
                    .select(ProductSku::getStock, ProductSku::getCostPrice,
                            ProductSku::getOriginalPrice, ProductSku::getSellPrice).list();
            if (null != skuList && !skuList.isEmpty()) {
                ProductSku productSku = skuList.get(0);
                record.setSellPrice(productSku.getSellPrice());
                record.setOriginalPrice(product.getOriginalPrice());
                record.setStock(productSku.getStock());
                record.setOriginalPrice(productSku.getOriginalPrice());
                if (productSku.getSellPrice() != null && productSku.getCostPrice() != null) {
                    record.setProfitPrice(productSku.getSellPrice().subtract(productSku.getCostPrice()));
                }
            }
            record.setProductMinImg(product.getProductMinImg());
            record.setSerialNum(product.getSerialNum());
            record.setTaxRate(dtl.getTaxRate());
        }
        record.setProductId(dtl.getProductId());
        record.setProductName(dtl.getProductName());
        record.setClassId(dtl.getProductCategoryId());
        record.setClassPathName(dtl.getProductCategoryName());
        record.setWarehouseId("1");
        record.setOperationUser(ThreadLocalUtil.getCurrentUser().getUserName());
        record.setGmtCreate(new Date());
    }

    private void assemblySupplierRecord(SecondarySupplierRecord record, OrderShipDtl dtl, OrderShip ship) {
        record.setOrderSn(dtl.getOrderSn());
        record.setProductId(dtl.getProductId());
        record.setProductName(dtl.getProductName());
        record.setSupplierId(dtl.getProductName());
        record.setSupplierName(dtl.getProductName());
        record.setPurchasingAgencyId(dtl.getDtlId());
        record.setPurchasingAgencyName(dtl.getDtlId());
        record.setProductType(ship.getProductType());
        record.setBidRateAmount(dtl.getTotalAmount());
        record.setNum(dtl.getShipNum());
        record.setWarehouseId("1");
        if (new BigDecimal(0).compareTo(dtl.getReturnCounts()) < 0) {
            record.setReturnTime(dtl.getReceiveTime());
        }
        record.setSettlementInfo(JSON.toJSONString(Collections.singletonList(dtl)));
        record.setOperationUser(ship.getReceiveName());
        record.setOperationUserPhone(ship.getReceivePhone());

    }

    @Override
    public PageUtils selectShipList(JSONObject jsonObject, LambdaQueryWrapper<OrderShip> q) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String enterpriseId = user.getEnterpriseId();
        q.eq(OrderShip::getEnterpriseId, enterpriseId);
        q.orderByDesc(OrderShip::getShipData);
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String type = (String) innerMap.get("type");
        String orderSn = (String) innerMap.get("orderSn");
        String billSn = (String) innerMap.get("billSn");
        String supplierName = (String) innerMap.get("supplierName");
//        if (supplierName != null && supplierName != "") {
//            q.eq(OrderShip::getSupplierName, supplierName);
//        }
        if (StringUtils.isNotBlank(supplierName)) {
            q.eq(OrderShip::getSupplierName, supplierName);
        }

        if (StringUtils.isNotBlank(orderSn)) {
            q.eq(OrderShip::getOrderSn, orderSn);
        }
        if (StringUtils.isNotBlank(billSn)) {
            q.eq(OrderShip::getBillSn, billSn);
        }


        if (type != null) {
            q.eq(OrderShip::getType, type);
        }
        q.orderByAsc(OrderShip::getShipData);
        IPage<OrderShip> page = this.page(
                new Query<OrderShip>().getPage(jsonObject),
                q
        );
        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateShipType(OrderShip orderShip) {
        String deliveryFlowId = orderShip.getDeliveryFlowId();
        if (deliveryFlowId == null || deliveryFlowId == "") {
            throw new BusinessException(500, "该用户没有填写发货单号");
        }
        String company = orderShip.getLogisticsCompany();
        if (company == null || company == "") {
            throw new BusinessException(1002, "该用户没有填写物流公司");
        }
        OrderShip info = getById(orderShip.getBillId());
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        if (!info.getShipEnterpriseId().equals(user.getEnterpriseId())) {
            throw new BusinessException(500, "该用户没有发货权限");
        }
        if (info != null) {
            if (info.getType() == 0) {
                info.setDeliveryFlowId(orderShip.getDeliveryFlowId());
                info.setLogisticsCompany(orderShip.getLogisticsCompany());
                info.setSupplierCode(user.getSocialCreditCode());
                info.setType(1);
                info.setShipData(new Date());
                info.setShipUserId(user.getUserId());
                update(info);
                /***
                 *  查看订单项是否发货完,修改订单状态
                 */
                orderItemService.updateOrderStateAndOrderItemByShip(info);

            } else {
                throw new BusinessException(302, "该发货单已经发货");
            }

        } else {
            throw new BusinessException(302, "该发货单不存在");
        }
    }


    /**
     * 供应商查询发货单
     *
     * @param outBillId
     * @return
     */
    @Override
    public OrderShip getDataByOutBillId(String outBillId) {
        LambdaQueryWrapper<OrderShip> q = new LambdaQueryWrapper<>();
        q.eq(OrderShip::getOutBillId, outBillId);
        OrderShip one = getOne(q);
        return one;
    }

//    /**
//     * 供应商查询发货单
//     *
//     * @param orderId
//     * @return
//     */

//    public void isFilshOrder(String orderId) {
//        Orders orders = orderItemService.isFilshAllOrderItem(orderId);
//        if (orders.getState() == 10) {
//            List<OrderShip> list = getListByOrderId(orderId);
//            for (OrderShip orderShip : list) {
//                if (!orderShip.getType().equals(2)) {
//                    orders.setState(9);
//                }
//            }
//        }
//        ordersService.update(orders);
//    }

    @Override
    public List<OrderShip> getListByOrderId(String ordersId) {
        LambdaQueryWrapper<OrderShip> q = new LambdaQueryWrapper<OrderShip>();
        q.eq(OrderShip::getOrderId, ordersId);
        List<OrderShip> list = list(q);
        return list;
    }

    @Override
    public List<OrderShip> getListByOrderIdAndType(String orderId) {
        return null;
    }


//    private Orders updateOrder(OrderShipVo orderShipVo, Orders orders) {
//        //设置订单状态
//
//        if (orderShipVo.getDeliveryFlowId() != null && orderShipVo.getDeliveryFlowId() != "") {
//            if (orders.getState() < 8) {
//                orderShipVo.setShipData(new Date());
//                orders.setState(8);
//                orders.setDeliveryTime(new Date());
//                ordersService.update(orders);
//            }
//            if (orders.getOrderClass() == 2 && orders.getParentOrderId() != null) {
//                Orders mainOrder = ordersService.getById(orders.getParentOrderId());
//                if (mainOrder.getState() < 8) {
//                    mainOrder.setState(8);
//                    mainOrder.setDeliveryTime(new Date());
//                    ordersService.update(mainOrder);
//                }
//
//            }
//
//        }
//
//        //如果为二级订单，必须更改主订单的信息
//
//        return orders;
//    }


    @Override
    public void returnGoods(ReturnGoodsVo returnGoodsVo) {
//        String billId = returnGoodsVo.getBillId();
////        OrderShipDtl dtl = orderShipDtlService.getDtlListByShipIdAndProductId(one.getBillId();
//        OrderShipDtl  dtl= orderShipDtlService.getById(returnGoodsVo.getDtlId());
//        if (dtl.getReturnCounts() == new BigDecimal(0)) {
//            dtl.setReturnCounts(returnGoodsVo.getReturnCounts());
//        } else {
//            dtl.setReturnCounts(returnGoodsVo.getReturnCounts().add(dtl.getReturnCounts()));
//        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFiles(ShipFiles shipFiles) {

        if (shipFiles.getRelevanceId() != null) {

            OrderShip byId = super.getById(shipFiles.getRelevanceId());
            fileService.deleteBatchFileByRelevanceIdAndType(byId.getBillId(), shipFiles.getRelevanceType());
            List<File> files = shipFiles.getFiles();
            if (files != null && files.size() > 0) {
                for (File file : files) {
                    if (file.getRelevanceId() != null) {
                        if (byId != null) {
                            file.setRelevanceType(10);
                            fileService.save(file);
                        } else {
                            throw new BusinessException(500, "发货单不存在");
                        }
                    } else {
                        throw new BusinessException(500, "发货单id和发货单类型不存在");
                    }
                }
            }
        } else {
            throw new BusinessException(500, "发货单不能为空");
        }
    }

    @Override
    public void exportDataByBillId(String billId, HttpServletResponse response) {

        OrderShipInfoVo orderShipInfoVo = baseMapper.exportDataByBillId(billId);
        List<OrderShipDtl> listByIds = orderShipDtlService.getDataByOrderShipId(billId);
        orderShipInfoVo.setDtls(listByIds);
        try {
            String src = mallConfig.templateFormUrl;
            String url = mallConfig.miniProgram;
            Map<String, Object> dataMap = new HashMap<>();
            List<OrderShipDtl> dtls = orderShipInfoVo.getDtls();
            dataMap.put("shipData", DateUtil.getyyymmddHHmmss(orderShipInfoVo.getShipData()));
            dataMap.put("supplierName", orderShipInfoVo.getSupplierName());
            dataMap.put("receiveOrgName", orderShipInfoVo.getReceiveOrgName());
            dataMap.put("billSn", orderShipInfoVo.getBillSn());
            dataMap.put("remarks", orderShipInfoVo.getRemarks());
            dataMap.put("totalPrice", orderShipInfoVo.getRateAmount());
            if (!orderShipInfoVo.getSupplierId().equals(orderShipInfoVo.getShipEnterpriseId())) {
                dataMap.put("remarks", "送货单位： " + orderShipInfoVo.getShipEnterpriseName());
            } else {
                dataMap.put("remarks", "");
            }
            dataMap.put("dataList", dtls);
            ExcelForWebUtil.exportExcelQrCode(response, dataMap, "发货单模板.xlsx", src, "发货单.xlsx", url + billId);
        } catch (Exception e) {
            log.error("导出失败信息：" + e.getMessage());
            throw new BusinessException(500, "导出失败！");
        }


    }


    @Override
    public void exportDataTwoById(String id, HttpServletResponse response) {
        OrderShip orderShip = getById(id);
        String src = mallConfig.templateFormUrl;
        String url = mallConfig.miniProgram;
        Map<String, Object> dataMap = new HashMap<>();

        UserLogin user = ThreadLocalUtil.getCurrentUser();

        try {
            dataMap.put("shipData", DateUtil.getyyymmddHHmmss(orderShip.getShipData()));
            dataMap.put("billSn", orderShip.getBillSn());
            List<OrderShipDtl> dtls = new ArrayList<>();
            if (orderShip.getSupplierId().equals(user.getEnterpriseId())) {
                dtls = orderShipDtlService.getDataByOrderShipId(id);
                dataMap.put("supplierName", orderShip.getSupplierName());
                dataMap.put("receiveOrgName", orderShip.getReceiveOrgName());
                dataMap.put("totalPrice", orderShip.getRateAmount());
                dataMap.put("remarks", "送货单位： " + orderShip.getShipEnterpriseName());
            } else {
                dataMap.put("supplierName", orderShip.getShipEnterpriseName());
                dataMap.put("receiveOrgName", orderShip.getSupplierName());
                dataMap.put("totalPrice", orderShip.getOtherRateAmount());
                dataMap.put("remarks", "");
                dtls = orderShipDtlService.getTwoOrderShipByBillid(id);
            }
            dataMap.put("dataList", dtls);
            /// 增加合计数量
            BigDecimal totalCounts = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);

            if (dtls != null) {
                totalCounts = dtls.stream()
                        .map(item -> Optional.ofNullable(item.getShipCounts()).orElse(BigDecimal.ZERO))
                        .reduce(totalCounts, BigDecimal::add);
            }
            dataMap.put("totalCounts", totalCounts);
            ExcelForWebUtil.exportExcelQrCode(response, dataMap, "发货单模板.xlsx", src, "发货单.xlsx", url + id);
        } catch (Exception e) {
            log.error("导出失败信息：" + e.getMessage());
            throw new BusinessException(500, "导出失败！");
        }

    }


    @Override
    public void exportDataPurchase(String id, HttpServletResponse response) {
        OrderShipInfoVo orderShipInfoVo = baseMapper.exportDataByBillId(id);
        if (orderShipInfoVo.getConfirmTime() == null) {
            throw new BusinessException(500, "数据错误，收货时间不能为空！");
        }
        try {
            String src = mallConfig.templateFormUrl;
            Map<String, Object> dataMap = new HashMap<>();
            List<OrderShipDtl> dtls = orderShipDtlService.lambdaQuery().eq(OrderShipDtl::getBillSn, orderShipInfoVo.getBillSn()).list();
            dataMap.put("confirmTime", DateUtil.getyyymmddHHmmss(orderShipInfoVo.getConfirmTime()));
            dataMap.put("supplierName", orderShipInfoVo.getSupplierName());
            dataMap.put("receiveOrgName", orderShipInfoVo.getReceiveOrgName());
            dataMap.put("orderSn", orderShipInfoVo.getOrderSn());
            dataMap.put("billSn", orderShipInfoVo.getBillSn());
            dataMap.put("remarks", orderShipInfoVo.getRemarks());
            dataMap.put("totalPrice", orderShipInfoVo.getRateAmount());
            dataMap.put("dataList", dtls);
            if (dtls == null) {

                throw new BusinessException(500, "物资入库单有误，明细不能为空");
            }
            ExcelForWebUtil.exportExcel(response, dataMap, "物资入库单模板.xlsx", src, "物资入库单.xlsx");
        } catch (Exception e) {
            log.error("导出失败信息：" + e.getMessage());
            throw new BusinessException(500, "导出失败！");
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createPCWPOrderRetuen(ReturnGoodsVo dto) {
        //获取密钥key
        String idStr = dto.getKeyId();
        //本地修改 参数虎汇总
        HashMap<String, Object> vo = new HashMap<>();
        //退货数据集合
        ArrayList<String> orderReturnIdList = new ArrayList<>();
        //    本地修改订单项参数集合保存
        ArrayList<HashMap<String, Object>> orderItems = new ArrayList<>();

        // 大宗退货明细参数集合
        ArrayList<HashMap<String, Object>> planDtlList = new ArrayList<>();
        //本地修改发货单项参数集合
        ArrayList<OrderReturnShipDtlVo> dtlVos = new ArrayList<>();
        //物资Id收料单集合ids
        List<ReturnGoodItemVo> dtls = dto.getDtls();
        //创建退货单项集合
        ArrayList<OrderReturnItem> itemLists = new ArrayList<>();

        if (dtls != null && dtls.size() > 0) {
            //查询所有退货物资数  （不同物资的退货）
            for (ReturnGoodItemVo dtl : dtls) {
                //针对单个物资经行查询
                String outDtlIds = dtl.getOutDtlIds();
                //针对订单进行合并，同一个订单的同多个发货单项，查询同一个订单项的所有发货单项数据
                String[] split = outDtlIds.split(",");
                BigDecimal returnCounts = dtl.getReturnCounts();

                if (returnCounts.compareTo(BigDecimal.valueOf(0)) == 0) {
                    throw new BusinessException(500, "退货数量不能为空");
                }
                //查询退货的所有收料单集合
                List<OrderShipDtl> list = orderShipDtlService.getListByIds(split);

                if (list != null && list.size() > 0) {

                    //获取单个收料项单数据，获取订单项-获取收料单项创建退货单项数据
                    OrderShipDtl orderShipDtl = list.get(0);

                    //收集修改后订单项数据
                    HashMap<String, Object> itemMap = new HashMap<>();
                    itemMap.put("orderItemId", orderShipDtl.getOrderItemId());
                    itemMap.put("returnCounts", dtl.getReturnCounts());
                    OrderShip orderShip = getById(orderShipDtl.getBillId());
                    // 根据发货单（收料）得到的订单项
                    OrderItem item = orderItemService.getById(orderShipDtl.getOrderItemId());
                    if (item.getShipCounts().subtract(item.getPcwpReturn()).compareTo(returnCounts) < 0) {
                        throw new BusinessException(500, "发货单编号" + orderShipDtl.getDtlId() + "订单数量是" + item.getConfirmCounts() + ",PCWP退货数量" + returnCounts + ",退货数量是不能大于收货数量");
                    }
                    Orders orders = ordersService.getById(orderShipDtl.getOrderId());
                    //根据订单项数据生成退货单项数据
                    OrderReturnItem orderReturnItem = new OrderReturnItem();
                    orderItemCopyOrderReturnItem(item, orderReturnItem);
                    orderReturnItem.setOrderSn(item.getOrderSn());
                    orderReturnItem.setOrderId(item.getOrderId());
                    orderReturnItem.setOrderItemId(item.getOrderItemId());
                    orderReturnItem.setCount(dtl.getReturnCounts());
                    BigDecimal add = item.getPcwpReturn().add(dtl.getReturnCounts());
                    item.setPcwpReturn(add);
                    // 大宗无价格
                    if (item.getProductType() != 12) {
                        //修改订单项信息
                        orderItemService.createAmouns(item, orderReturnItem, dtl.getReturnCounts());
                    }
                    if (item.getParentOrderItemId() != null) {
                        OrderItem main0rderItem = orderItemService.getById(item.getParentOrderItemId());
                        main0rderItem.setPcwpReturn(item.getPcwpReturn());
                        // 如果是手动完结，记录最大发货数量
                        if (orders.getFinishType() == 1) {
                            main0rderItem.setCloseMaxQty(dtl.getReturnCounts());
                        }
                        orderItemService.update(main0rderItem);
                    } else {
                        OrderItem son = orderItemService.getOrderItemByParentId(item.getOrderItemId());
                        if (son != null) {
                            son.setPcwpReturn(item.getPcwpReturn());
                            // 如果是手动完结，记录最大发货数量
                            if (orders.getFinishType() == 1) {
                                son.setCloseMaxQty(dtl.getReturnCounts());
                            }
                            orderItemService.update(son);
                        }
                    }

                    orderItemService.update(item);
                    if (orderShip.getSourceType() == 2) {
                        //本地sql查询，查询sku信息，防止用户删除商品信息，查询不到数据
                        ProductSku sku = productSkuService.getLocalDataList(item.getSkuId());
                        sku.setStock(sku.getStock().add(dtl.getReturnCounts()));
                        productSkuService.update(sku);
//                            if (sku != null) {
//                                HashMap<String, Object> skuMap = new HashMap<>();
//                                skuMap.put("skuId", sku.getSkuId());
//                                // 现场收料时候的退货数量（后面减库存用）
//                                skuMap.put("returnCounts", dtl.getReturnCounts());
//                                stockList.add(skuMap);
//                            }


                        //如果是二级订单，获取二级订单信息，更改二级订单
                        if (orders.getOrderClass() != 1) {
                            OrderItem son = orderItemService.getOrderItemByParentId(item.getOrderItemId());
                            if (son != null) {
                                HashMap<String, Object> sonItem = new HashMap<>();
                                sonItem.put("orderItemId", son.getOrderItemId());
                                sonItem.put("returnCounts", dtl.getReturnCounts());
                                orderReturnItem.setOtherOrderItemId(son.getOrderItemId());
                                orderReturnItem.setOrderSn(son.getOrderSn());
                                orderReturnItem.setOrderId(son.getOrderId());
                                orderItems.add(sonItem);
                            }
                        }

                    } else if (orderShip.getSourceType() == 6) {
                        ProductSku sku = productSkuService.getLocalDataList(item.getSkuId());
                        sku.setStock(sku.getStock().add(dtl.getReturnCounts()));
                        productSkuService.update(sku);
//                            if (sku != null) {
//                                HashMap<String, Object> skuMap = new HashMap<>();
//                                skuMap.put("skuId", sku.getSkuId());
//                                skuMap.put("returnCounts", dtl.getReturnCounts());
//                                stockList.add(skuMap);
//                            }


                        //如果是二级订单，获取二级订单信息，更改二级订单
                        if (orders.getOrderClass() != 1) {
                            OrderItem son = orderItemService.getOrderItemByParentId(item.getOrderItemId());
                            if (son != null) {
                                HashMap<String, Object> sonItem = new HashMap<>();
                                sonItem.put("orderItemId", son.getOrderItemId());
                                sonItem.put("returnCounts", dtl.getReturnCounts());
                                orderReturnItem.setOtherOrderItemId(son.getOrderItemId());
                                orderReturnItem.setOrderSn(son.getOrderSn());
                                orderReturnItem.setOrderId(son.getOrderId());
                                orderItems.add(sonItem);
                            }
                        }
                    }
                    // 处理大宗月供退货逻辑  // 供应商区分二级 拆分二级订单
                    else {
                        // 大宗月供计划存在二级供应商
                        if (orders.getOrderClass() != 1 && orderShip.getSourceType() == 1) {
                            // 大宗的二级订单 item是被拆分的订单项
                            OrderSelectPlan orderPlan = orderSelectPlanService.getDataByorderItemId(item.getParentOrderItemId());
                            MaterialMonthSupplyPlanDtl planDtl = materialMonthSupplyPlanDtlService.getById(orderPlan.getDtlId());
                            // 根据发货单获取发货数量
                            // 查询当前订单的计划明细
                            BigDecimal orderQty = planDtl.getOrderQty();
                            if (orderQty.compareTo(dtl.getReturnCounts()) < 0) {
                                throw new BusinessException(500, "退货数量不能大于已生成订单明细数量" + orderQty + "");
                            } else {
                                planDtl.setOrderQty(orderQty.subtract(dtl.getReturnCounts()));
                            }
                            HashMap<String, Object> hashMap = new HashMap<>();
                            hashMap.put("id", planDtl.getPlanDtlId());
                            hashMap.put("returnCounts", dtl.getReturnCounts());
                            planDtlList.add(hashMap);
                            // 修改计划中的下单数量
                            materialMonthSupplyPlanDtlService.update(planDtl);
                            // 构建子订单退货项
                            OrderItem secondOrderItem = orderItemService.getOrderItemByParentId(item.getParentOrderItemId());
                            if (secondOrderItem != null) {
                                HashMap<String, Object> sonItem = new HashMap<>();
                                sonItem.put("orderItemId", secondOrderItem.getOrderItemId());
                                sonItem.put("returnCounts", dtl.getReturnCounts());
                                orderReturnItem.setOtherOrderItemId(secondOrderItem.getOrderItemId());
                                orderReturnItem.setOrderSn(secondOrderItem.getOrderSn());
                                orderReturnItem.setOrderId(secondOrderItem.getOrderId());
                                orderItems.add(sonItem);
                            }

                        } else {
                            // 当前选择的大宗月供计划
                            OrderSelectPlan orderPlan = orderSelectPlanService.getDataByorderItemId(item.getOrderItemId());
                            //大宗月供计划明细
                            MaterialMonthSupplyPlanDtl planDtl = materialMonthSupplyPlanDtlService.getById(orderPlan.getDtlId());
                            // （月供计划中）已经下单的数量
                            BigDecimal orderQty = planDtl.getOrderQty();
                            if (mallConfig.isCountPlanOrderNum == 1) {
                                // TODO 计划最新统计
                                BigDecimal qty = ordersService.getOrderUseCountBySelectPlanDtlIds(planDtl.getPlanDtlId());
                                orderQty = qty;
                            }
                            if (orderQty.compareTo(dtl.getReturnCounts()) < 0) {
                                throw new BusinessException(500, "退货数量不能大于已生成订单明细数量" + orderQty + "");
                            } else {
                                planDtl.setOrderQty(orderQty.subtract(dtl.getReturnCounts()));
                            }
                            HashMap<String, Object> hashMap = new HashMap<>();
                            hashMap.put("id", planDtl.getPlanDtlId());
                            hashMap.put("returnCounts", dtl.getReturnCounts());
                            planDtlList.add(hashMap);
                            // 修改计划中的下单数量
                            materialMonthSupplyPlanDtlService.update(planDtl);
                        }
                    }
                    orderItems.add(itemMap);
                    itemLists.add(orderReturnItem);
                } else {
                    throw new BusinessException(500, "收料单不存在，收料单发送错误");
                }
                // 处理发货单中构建退货单
                for (OrderShipDtl shipDtl : list) {
                    OrderReturnShipDtlVo dtlVo = new OrderReturnShipDtlVo();
                    //如果发货数量==退货数量  跳过循环
                    if (shipDtl.getShipNum().compareTo(shipDtl.getReturnCounts()) == 0) {
                        continue;
                    } else {
                        //  退货总数量>(确认发货数量-已退货数量）
                        BigDecimal subtract = shipDtl.getShipNum().subtract(shipDtl.getReturnCounts()); // (还可以退货的数量)
                        // PCWP退货数量
                        // PCWP退货总数量 - （已确认收获数量 - PCWP已经退货数量） returnCounts > subtract
                        int i = returnCounts.subtract(subtract).compareTo(BigDecimal.valueOf(0));
                        // 是否还可以退货 i>0 全退
                        if (i > 0) {
                            //得到新的总退货数量
                            returnCounts = returnCounts.subtract(subtract);

                            //本次退货数量
                            BigDecimal nowReturnCount = shipDtl.getShipNum().subtract(shipDtl.getReturnCounts());
                            //这个退货数量是 ==》记录发货单中
                            shipDtl.setReturnCounts(shipDtl.getShipNum());


                            //保存发货单项编号和本次退货数量，回调时修改发货单项数据
                            dtlVo.setDtlId(shipDtl.getDtlId());
                            dtlVo.setReturnCounts(nowReturnCount);
                            dtlVos.add(dtlVo);
                        } else {

                            //本次退货数量
                            BigDecimal nowReturnCount = returnCounts.subtract(shipDtl.getReturnCounts());
                            shipDtl.setReturnCounts(returnCounts.add(shipDtl.getReturnCounts()));

                            dtlVo.setReturnCounts(shipDtl.getReturnCounts());

                            //保存发货单项编号和本次退货数量，回调时修改发货单项数据
                            dtlVo.setDtlId(shipDtl.getDtlId());
                            dtlVo.setReturnCounts(nowReturnCount);

                            dtlVos.add(dtlVo);
                            break;
                        }
                    }
                }
            }

        } else {
            throw new BusinessException(500, "退货项不存在");
        }
        // itemLists 是构建的退货的明细项  orders 是该退货对应的订单
        Map<String, List<OrderReturnItem>> orderReturnList = itemLists.stream()
                .collect(Collectors.groupingBy(OrderReturnItem::getOrderId));
        orderReturnList.forEach((orderId, orderReturnItems) -> {
            // 构建退货单
            OrderReturn orderReturn = new OrderReturn();
            Orders son = ordersService.getById(orderId);
            if (son != null) {
                if (son.getParentOrderId() != null) {
                    Orders parentOrder = ordersService.getById(son.getParentOrderId());
                    parentOrder.setState(8);
                    ordersService.update(parentOrder);
                    orderReturn.setBillType(parentOrder.getBillType());
                    orderReturn.setEnterpriseId(parentOrder.getEnterpriseId());
                    orderReturn.setSupplierId(parentOrder.getSupplierId());
                    orderReturn.setSupplierName(parentOrder.getSupplierName());
                    orderReturn.setEnterpriseId(parentOrder.getEnterpriseId());
                    orderReturn.setEnterpriseName(parentOrder.getEnterpriseName());
                    orderReturn.setOrderId(parentOrder.getOrderId());
                    orderReturn.setOrderSn(parentOrder.getOrderSn());
                    orderReturn.setOrderClass(2);
                    orderReturn.setOtherOrderSn(son.getOrderSn());
                    orderReturn.setOtherOrderId(son.getOrderId());
                    if (son.getProductType() == 10 || son.getProductType() == 11) {
                        orderReturn.setSourceType(2);
                        orderReturn.setShopId(parentOrder.getShopId());
                        orderReturn.setShopName(parentOrder.getShopName());
                    } else if (parentOrder.getProductType() == 12) {
                        orderReturn.setSourceType(1);
                    } else if (parentOrder.getProductType() == 13) {
                        orderReturn.setShopId(parentOrder.getShopId());
                        orderReturn.setShopName(parentOrder.getShopName());
                        orderReturn.setSourceType(6);
                    }
                } else {
                    orderReturn.setBillType(son.getBillType());
                    orderReturn.setEnterpriseId(son.getEnterpriseId());
                    orderReturn.setSupplierId(son.getSupplierId());
                    orderReturn.setSupplierName(son.getSupplierName());
                    orderReturn.setEnterpriseId(son.getEnterpriseId());
                    orderReturn.setEnterpriseName(son.getEnterpriseName());
                    orderReturn.setOrderId(son.getOrderId());
                    orderReturn.setOrderSn(son.getOrderSn());
                    orderReturn.setOrderClass(1);
                    if (son.getProductType() == 10 || son.getProductType() == 11) {
                        orderReturn.setSourceType(2);
                        orderReturn.setShopId(son.getShopId());
                        orderReturn.setShopName(son.getShopName());
                    } else if (son.getProductType() == 12) {
                        orderReturn.setSourceType(1);
                    } else if (son.getProductType() == 13) {
                        orderReturn.setShopId(son.getShopId());
                        orderReturn.setShopName(son.getShopName());
                        orderReturn.setSourceType(6);
                    }
                }
                son.setState(8);
                ordersService.update(son);
            }

            String orderReturnNo = UUID.randomUUID().toString().replace("-", "");
            orderReturn.setOrderReturnNo(orderReturnNo);
            orderReturn.setIsOut(1);
            orderReturn.setShipEnterpriseName(son.getSupplierName());
            orderReturn.setShipEnterpriseId(son.getSupplierId());


            orderReturn.setFlishTime(new Date());
//               orderReturn.setFounderId(dto.getFounderId());
            orderReturn.setFounderName(dto.getFounderName());
            orderReturn.setRemarks(dto.getRemark());
            orderReturn.setGmtCreate(new Date());
            orderReturn.setState(3);
            orderReturnService.save(orderReturn);

            BigDecimal otherNoRateAmount = BigDecimal.valueOf(0);
            BigDecimal otherRateAmount = BigDecimal.valueOf(0);
            BigDecimal noRateAmount = BigDecimal.valueOf(0);
            BigDecimal rateAmount = BigDecimal.valueOf(0);
            StringBuilder untitled = new StringBuilder();
            for (OrderReturnItem orderReturnItem : orderReturnItems) {
                // 退货子明细关联退货主数据
                orderReturnItem.setIsOut(1);
                orderReturnItem.setOrderReturnId(orderReturn.getOrderReturnId());
                orderReturnItem.setOrderReturnNo(orderReturn.getOrderReturnNo());

                orderReturnItem.setOrderId(orderReturn.getOrderId());
                orderReturnItem.setOrderSn(orderReturn.getOrderSn());
                orderReturnItem.setOtherOrderId(orderReturn.getOtherOrderId());
                orderReturnItem.setOtherOrderSn(orderReturn.getOtherOrderSn());

                untitled.append(orderReturnItem.getProductName()).append(",");
                // 大宗合同无价格和金额，其他类型都需要获取金额并更新退货总金额等
                if (son.getProductType() != 12) {
                    if (orderReturn.getOrderClass() != 1) {
                        otherNoRateAmount = otherNoRateAmount.add(orderReturnItem.getOtherNoRateAmount());
                        otherRateAmount = otherRateAmount.add(orderReturnItem.getOtherRateAmount());
                    }
                    rateAmount = rateAmount.add(orderReturnItem.getTotalAmount());
                    noRateAmount = noRateAmount.add(orderReturnItem.getNoRateAmount());
                }
            }
            orderReturn.setUntitled(untitled.toString());
            orderReturn.setRateAmount(rateAmount);
            orderReturn.setTotalAmount(rateAmount);
            orderReturn.setNoRateAmount(noRateAmount);
            if (son.getProductType() != 12) {
                orderReturn.setOtherNoRateAmount(otherNoRateAmount);
                orderReturn.setOtherRateAmount(otherRateAmount);
            }
            orderReturnService.updateById(orderReturn);
            //保存退货编号，回调时删除数据
            orderReturnIdList.add(orderReturn.getOrderReturnId());
            ordersService.closeOrder2(orderReturn.getOrderId());

        });

        orderReturnItemService.saveBatch(itemLists);
        vo.put("shipDtl", dtlVos);
        vo.put("orderReturnId", orderReturnIdList);
        vo.put("orderItemList", orderItems);
        vo.put("planDtl", planDtlList);

        InterfaceLogs iLog = new InterfaceLogs();
        iLog.setSecretKey(idStr);
        iLog.setClassPackage(OrderShipServiceImpl.class.getName());
        iLog.setMethodName("createPCWPOrderRetuen");
        iLog.setLocalArguments(JSON.toJSONString(vo));
        iLog.setFarArguments(JSON.toJSONString(dto));
        iLog.setIsSuccess(1);
        iLog.setLogType(3);
        interfaceLogsService.create(iLog);
        LogUtil.writeInfoLog(idStr, "createPCWPOrderRetuen", vo, dto, null, OrderShipServiceImpl.class);


    }


    @Transactional(rollbackFor = Exception.class)
    public void createPCWPOrderRetuen2(ReturnGoodsVo dto) {

        //获取密钥key
        String idStr = dto.getKeyId();
        List<ReturnGoodItemVo> dtls = dto.getDtls();
        if (dtls == null || dtls.isEmpty()) {
            throw new BusinessException(500, "退货项不存在");
        }
        List<String> dtlIds = dtls.stream()
                .map(ReturnGoodItemVo::getOutDtlIds) // 获取每个对象的 outDtlIds
                .filter(outDtlIds -> outDtlIds != null && !outDtlIds.isEmpty()) // 过滤掉 null 或空值
                .flatMap(outDtlIds -> Arrays.stream(outDtlIds.split(","))) // 拆分并展平为单个字符串流
                .collect(Collectors.toList());
        //获取所有的退货发货单数据
        List<OrderShipDtl> orderShipDtls = orderShipDtlService.listByIds(dtlIds);
        if (orderShipDtls == null || orderShipDtls.isEmpty()) {
            throw new BusinessException(500, "未获取到有效的退货发货单数据");
        }
        //退货项根据订单进行分组
        Map<String, List<OrderShipDtl>> orderList = orderShipDtls.stream().collect(Collectors.groupingBy(OrderShipDtl::getOrderId));
        //同一个订单的所有退货发货单数据
        orderList.forEach((orderId, orderShipDtl) -> {

            Orders byId = ordersService.getById(orderId);
            if (byId == null) {
                throw new BusinessException(500, "无法获取对应的订单信息，订单Id: " + orderId);
            }
            ArrayList<OrderReturnItem> returnItemList = new ArrayList<>();

            //区分一级订单和二级订单
            if (byId.getOrderClass() == 1) {
                OrderReturn orderReturn = new OrderReturn();
                createOrderReturn(orderReturn, byId);
                orderReturnService.save(orderReturn);
                //获取同一个订单的退货订单项id
                List<String> items = orderShipDtl.stream().map(OrderShipDtl::getOrderItemId).collect(Collectors.toList());
                //查询订单的所有订单项
                ArrayList<OrderItem> orderItems = (ArrayList<OrderItem>) orderItemService.listByIds(items);
                // 计算退货单金额
                BigDecimal noRateAmount = BigDecimal.valueOf(0);
                BigDecimal rateAmount = BigDecimal.valueOf(0);

                if (orderItems != null && !orderItems.isEmpty()) {
                    //同一个订单的所有退货发货单数据退货发货单数据，根据订单项进行分组
                    Map<String, List<OrderShipDtl>> orderItemIdMap = orderShipDtl.stream().collect(Collectors.groupingBy(OrderShipDtl::getOrderItemId));
                    //遍历退货订单项，
                    for (OrderItem item : orderItems) {
                        //设置退货项数据，有多少个订单项就有多少个退货项
                        OrderReturnItem orderReturnItem = new OrderReturnItem();
                        //根据订单项，退货单，生成退货的基本数据
                        createOrderReturnItem(item, orderReturnItem, orderReturn);
                        //获取同一个订单项的所有退货发货单项数据
                        ArrayList<OrderShipDtl> orderItemShipList = (ArrayList<OrderShipDtl>) orderItemIdMap.get(item.getOrderItemId());
                        //记录同一个订单项所有退货数量
                        BigDecimal returnCount = BigDecimal.ZERO;
                        for (ReturnGoodItemVo dtl : dtls) {
                            BigDecimal returnCounts = dtl.getReturnCounts();
                            if (returnCounts.compareTo(BigDecimal.ZERO.stripTrailingZeros()) == 0) {
                                throw new BusinessException(500, "退货数量不能为空");
                            }
                            for (OrderShipDtl shipDtl : orderItemShipList) {
                                if (dtl.getOutDtlIds().contains(shipDtl.getDtlId())) {
                                    returnCount = returnCount.add(dtl.getReturnCounts());
                                    break;
                                }
                            }
                        }
                        orderReturnItem.setCount(returnCount);
                        if (item.getConfirmCounts().subtract(item.getPcwpReturn()).compareTo(returnCount) < 0) {
                            throw new BusinessException(500, "订单编号" + item.getOrderSn() + "订单收货数量是" + item.getConfirmCounts() + ",PCWP已退货数量" + item.getPcwpReturn() + "本次退货数量:" + returnCount + ",退货数量是不能大于收货数量");
                        }
                        BigDecimal add = item.getPcwpReturn().add(returnCount);
                        item.setPcwpReturn(add);
                        if (byId.getFinishType() == 1) {
                            item.setCloseMaxQty(returnCount);
                        }

//
//                            returnItemList.add(orderReturnItem);

                        if (item.getProductType() != 12) {
                            //修改订单项信息
                            orderItemService.createOneAmouns(item, orderReturnItem, returnCount);
                            rateAmount = rateAmount.add(orderReturnItem.getTotalAmount());
                            noRateAmount = noRateAmount.add(orderReturnItem.getNoRateAmount());
                            ProductSku sku = productSkuService.getLocalDataList(item.getSkuId());
                            if (sku != null) {
                                sku.setStock(sku.getStock().add(returnCount));
                                productSkuService.update(sku);
                            }

                        } else {
                            OrderSelectPlan orderPlan = orderSelectPlanService.getDataByorderItemId(item.getParentOrderItemId());
                            MaterialMonthSupplyPlanDtl planDtl = materialMonthSupplyPlanDtlService.getById(orderPlan.getDtlId());
                            // 查询当前订单的计划明细
                            BigDecimal orderQty = planDtl.getOrderQty();
                            planDtl.setOrderQty(orderQty.subtract(returnCount));
                            materialMonthSupplyPlanDtlService.update(planDtl);
                        }
                        // 如果是手动完结，记录最大发货数量
                        orderItemService.update(item);
                        returnItemList.add(orderReturnItem);
                    }
                }
                //保存keyId,回滚数据的时候按照keyID删除退货数据
                orderReturn.setOutKeyId(idStr);
                orderReturn.setFounderName(dto.getFounderName());
                orderReturn.setRemarks(dto.getRemark());
                orderReturn.setRateAmount(rateAmount);
                orderReturn.setTotalAmount(rateAmount);
                orderReturn.setNoRateAmount(noRateAmount);
                orderReturnService.updateById(orderReturn);
            } else {
                //查询订单的所有订单项目
                List<String> dtlIdList = orderShipDtl.stream().map(OrderShipDtl::getDtlId).collect(Collectors.toList());
                List<OrderReturnItemPCWPVo> orderReturnItems = baseMapper.selectOtherItemsByOrderId(dtlIdList);
                Map<String, List<OrderReturnItemPCWPVo>> otherOrderList = orderReturnItems.stream().distinct().collect(Collectors.groupingBy(OrderReturnItemPCWPVo::getOtherOrderId));
                otherOrderList.forEach((otherOrderId, orderReturnList) -> {
                    OrderReturn orderReturn = new OrderReturn();
                    createOrderReturn(orderReturn, byId);
                    //                        //设置二级退货信息
                    orderReturn.setShipEnterpriseId(orderReturnList.get(0).getShipEnterpriseId());
                    orderReturn.setShipEnterpriseName(orderReturnList.get(0).getShipEnterpriseName());
                    orderReturn.setOrderClass(2);
                    orderReturn.setOtherOrderId(otherOrderId);
                    orderReturn.setOtherOrderSn(orderReturnList.get(0).getOtherOrderSn());
                    orderReturn.setShopId(orderReturnList.get(0).getShopId());
                    orderReturn.setShopName(orderReturnList.get(0).getShopName());
                    orderReturnService.save(orderReturn);
                    BigDecimal noRateAmount = BigDecimal.valueOf(0);
                    BigDecimal rateAmount = BigDecimal.valueOf(0);
                    BigDecimal otherNoRateAmount = BigDecimal.valueOf(0);
                    BigDecimal otherRateAmount = BigDecimal.valueOf(0);
                    List<String> itemListId = orderReturnList.stream().map(OrderReturnItem::getOrderItemId).distinct().collect(Collectors.toList());
                    List<OrderItem> orderItems = orderItemService.listByIds(itemListId);
                    if (orderItems.size() == 0 || orderItems == null) {
                        throw new BusinessException(500, "订单明细不存在");
                    }
                    for (OrderItem item : orderItems) {
                        OrderReturnItem orderReturnItem = new OrderReturnItem();
                        orderItemCopyOrderReturnItem(item, orderReturnItem);
                        orderReturnItem.setOrderId(orderReturn.getOrderId());
                        orderReturnItem.setOrderSn(orderReturn.getOrderSn());
                        orderReturnItem.setOtherOrderSn(orderReturn.getOtherOrderSn());
                        orderReturnItem.setOtherOrderId(orderReturn.getOtherOrderId());
                        orderReturnItem.setOrderReturnId(orderReturn.getOrderReturnId());
                        orderReturnItem.setOrderReturnNo(orderReturn.getOrderReturnNo());
                        BigDecimal returnCount = BigDecimal.ZERO;

                        for (OrderReturnItemPCWPVo orderReturnItemPCWPVo : orderReturnList) {
                            if (item.getOrderItemId().equals(orderReturnItemPCWPVo.getOrderItemId())) {
                                orderReturnItem.setOtherOrderItemId(orderReturnItemPCWPVo.getOtherOrderItemId());
                                orderReturnItem.setOrderItemId(orderReturnItemPCWPVo.getOrderItemId());
                                orderReturnItem.setProductPrice(orderReturnItemPCWPVo.getProductPrice());
                                orderReturnItem.setNoRatePrice(orderReturnItemPCWPVo.getNoRatePrice());
                                orderReturnItem.setOtherProductPrice(orderReturnItemPCWPVo.getOtherProductPrice());
                                orderReturnItem.setOtherNoProductPrice(orderReturnItemPCWPVo.getOtherNoProductPrice());
                                for (ReturnGoodItemVo dtl : dtls) {
                                    if (orderReturnItemPCWPVo.getDtlId().contains(dtl.getOutDtlIds())) {
                                        returnCount = returnCount.add(dtl.getReturnCounts());
                                        break;
                                    }

                                }

                            }
                        }
                        if (returnCount.compareTo(BigDecimal.valueOf(0)) == 0) {
                            throw new BusinessException(500, "退货数量不能为空");
                        }
                        if (item.getConfirmCounts().subtract(item.getPcwpReturn()).compareTo(returnCount) < 0) {
                            throw new BusinessException(500, "订单编号" + item.getOrderSn() + "订单收货数量是" + item.getConfirmCounts() + ",PCWP已退货数量" + item.getPcwpReturn() + "本次退货数量:" + returnCount + ",退货数量是不能大于收货数量");
                        }
                        BigDecimal add = item.getPcwpReturn().add(returnCount);
                        if (item.getProductType() != 12) {
                            //修改订单项信息
                            //含税总
                            orderReturnItem.setTotalAmount(returnCount.multiply(orderReturnItem.getProductPrice()));
                            orderReturnItem.setOtherRateAmount(returnCount.multiply(orderReturnItem.getOtherProductPrice()));
                            BigDecimal aa = TaxCalculator.calculateNotTarRateAmount(orderReturnItem.getOtherProductPrice(), item.getTaxRate());
                            orderReturnItem.setOtherNoProductPrice(aa);
                            BigDecimal notRateAmount = TaxCalculator.noTarRateItemAmount(orderReturnItem.getTotalAmount(), orderReturnItem.getNoRatePrice(), returnCount, item.getTaxRate());

                            BigDecimal notOtherRateAmount = TaxCalculator.noTarRateItemAmount(orderReturnItem.getOtherRateAmount(), orderReturnItem.getOtherNoProductPrice(), returnCount, item.getTaxRate());
                            //不含税总
                            orderReturnItem.setNoRateAmount(notRateAmount);
                            orderReturnItem.setOtherNoRateAmount(notOtherRateAmount);

                            rateAmount = rateAmount.add(orderReturnItem.getTotalAmount());
                            noRateAmount = noRateAmount.add(notRateAmount);
                            otherNoRateAmount = otherNoRateAmount.add(notOtherRateAmount);
                            otherRateAmount = otherRateAmount.add(orderReturnItem.getOtherRateAmount());
                            ProductSku sku = productSkuService.getLocalDataList(item.getSkuId());
                            sku.setStock(sku.getStock().add(returnCount));
                            productSkuService.update(sku);
                        } else {
                            OrderSelectPlan orderPlan = orderSelectPlanService.getDataByorderItemId(item.getParentOrderItemId());
                            MaterialMonthSupplyPlanDtl planDtl = materialMonthSupplyPlanDtlService.getById(orderPlan.getDtlId());
                            // 查询当前订单的计划明细
                            BigDecimal orderQty = planDtl.getOrderQty();
                            planDtl.setOrderQty(orderQty.subtract(returnCount));
                            materialMonthSupplyPlanDtlService.update(planDtl);
                        }
                        item.setPcwpReturn(add);
                        orderItemService.lambdaUpdate().set(OrderItem::getPcwpReturn, add).eq(OrderItem::getOrderItemId, item.getParentOrderItemId()).update();
                        orderItemService.updateById(item);
                        orderReturnItem.setCount(returnCount);
                        returnItemList.add(orderReturnItem);
                    }
                    orderReturn.setOutKeyId(idStr);
                    orderReturn.setFounderName(dto.getFounderName());
                    orderReturn.setRemarks(dto.getRemark());
                    orderReturn.setRateAmount(rateAmount);
                    orderReturn.setTotalAmount(rateAmount);
                    orderReturn.setNoRateAmount(noRateAmount);
                    orderReturn.setOtherRateAmount(otherRateAmount);
                    orderReturn.setOtherNoRateAmount(otherNoRateAmount);
                    orderReturnService.updateById(orderReturn);

                    //修改订单状态为代发货状态
                    ordersService.lambdaUpdate().set(Orders::getState, 8).eq(Orders::getOrderId, otherOrderId).update();
                });
                //修改订单状态为代发货状态
            }
            orderReturnItemService.saveBatch(returnItemList);
            ordersService.lambdaUpdate().set(Orders::getState, 8).eq(Orders::getOrderId, byId.getOrderId()).update();
        });

        InterfaceLogs iLog = new InterfaceLogs();
        iLog.setSecretKey(idStr);
        iLog.setClassPackage(OrderShipServiceImpl.class.getName());
        iLog.setMethodName("createPCWPOrderReturn");
        iLog.setLocalArguments(JSON.toJSONString(dto));
        iLog.setIsSuccess(1);
        iLog.setLogType(3);
        interfaceLogsService.create(iLog);


    }


    private static void orderItemCopyOrderReturnItem(OrderItem item, OrderReturnItem orderReturnItem) {
        orderReturnItem.setProductId(item.getProductId());
        orderReturnItem.setProductSn(item.getProductSn());
        orderReturnItem.setProductName(item.getProductName());
        orderReturnItem.setProductImg(item.getProductImg());
        orderReturnItem.setBuyCounts(item.getBuyCounts());
        orderReturnItem.setSkuId(item.getSkuId());
        orderReturnItem.setSkuName(item.getSkuName());
        orderReturnItem.setTexture(item.getTexture());
        orderReturnItem.setUnit(item.getUnit());
        orderReturnItem.setBrandId(item.getBrandId());
        orderReturnItem.setBrandName(item.getBrandName());
        orderReturnItem.setGmtCreate(new Date());
    }

    private void createOrderReturnItem(OrderItem item, OrderReturnItem orderReturnItem, OrderReturn orderReturn) {
        orderReturnItem.setOrderReturnId(orderReturn.getOrderReturnId());
        orderReturnItem.setOrderReturnNo(orderReturn.getOrderReturnNo());
        orderItemCopyOrderReturnItem(item, orderReturnItem);
        orderReturnItem.setOrderSn(item.getOrderSn());
        orderReturnItem.setOrderId(item.getOrderId());
        orderReturnItem.setOrderItemId(item.getOrderItemId());

    }

    private static void createOrderReturn(OrderReturn orderReturn, Orders byId) {
        String orderReturnNo = UUID.randomUUID().toString().replace("-", "");
        orderReturn.setOrderReturnNo(orderReturnNo);
        orderReturn.setUntitled(byId.getUntitled());
        orderReturn.setSupplierId(byId.getSupplierId());
        orderReturn.setSupplierName(byId.getSupplierName());
        orderReturn.setEnterpriseId(byId.getEnterpriseId());
        orderReturn.setEnterpriseName(byId.getEnterpriseName());
        orderReturn.setOrderId(byId.getOrderId());
        orderReturn.setOrderSn(byId.getOrderSn());
        orderReturn.setGmtCreate(new Date());
        orderReturn.setState(3);
        orderReturn.setFlishTime(new Date());
        orderReturn.setShipEnterpriseId(byId.getSupplierId());
        orderReturn.setShipEnterpriseName(byId.getSupplierName());
        orderReturn.setIsOut(1);
        orderReturn.setOrderClass(1);
        if (byId.getProductType() == 10 || byId.getProductType() == 11) {
            orderReturn.setSourceType(2);
            orderReturn.setShopId(byId.getShopId());
            orderReturn.setShopName(byId.getShopName());
        } else if (byId.getProductType() == 12) {
            orderReturn.setSourceType(1);
        } else if (byId.getProductType() == 13) {
            orderReturn.setShopId(byId.getShopId());
            orderReturn.setShopName(byId.getShopName());
            orderReturn.setSourceType(6);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rollBackReconciliationCreate(String keyId) {
        List<OrderReturn> skuList = orderReturnService.lambdaQuery().eq(OrderReturn::getOutKeyId, keyId).list();
        if (skuList != null) {
            skuList.forEach(orderReturn -> {
                //修改订单状态为待发货
                Orders byId = ordersService.getById(orderReturn.getOrderId());
                // 查询订单项
                // 修改退货订单项
                List<OrderReturnItem> orderReturnItems = orderReturnItemService.lambdaQuery().eq(OrderReturnItem::getOrderReturnId, orderReturn.getOrderReturnId()).list();
                Map<String, List<OrderReturnItem>> orderItemMapList = orderReturnItems.stream().collect(Collectors.groupingBy(OrderReturnItem::getOrderItemId));
                ArrayList<OrderItem> orderItems = new ArrayList<>();
                ArrayList<ProductSku> skuItemList = new ArrayList<>();
                //查询已发货的发货单数据
                List<OrderShip> orderShips = lambdaQuery().eq(OrderShip::getType, 2).eq(OrderShip::getOrderId, byId.getOrderId()).list();
                List<String> orderShipIds = orderShips.stream().map(OrderShip::getBillId).collect(Collectors.toList());
                byId.setState(8);
                orderItemMapList.forEach((key, list) -> {
                    OrderItem orderItem = orderItemService.getById(key);
                    BigDecimal pcwpCount = BigDecimal.ZERO;
                    for (OrderReturnItem orderReturnItem : list) {
                        pcwpCount = pcwpCount.add(orderReturnItem.getCount());
                        List<OrderShipDtl> dtlInfoList = orderShipDtlService.lambdaQuery().eq(OrderShipDtl::getBillId, orderShipIds)
                                .eq(OrderShipDtl::getOrderItemId, orderReturnItem.getOrderItemId()).list();
                        //如果orderReturnItem退货数量大于orderShipDtl发货单项的收货数量，count=退货数量-发货单项的收货数量，
                        // 修改发货项，继续比对下一条发货项，直到pcwpReturn=>下一条发货项的收货数量，修改发货单项的退货数量，结束循环
                        for (OrderShipDtl orderShipDtl : dtlInfoList) {
                            BigDecimal count = orderReturnItem.getCount();
                            if (count.compareTo(orderShipDtl.getShipNum()) >= 0) {
                                count = count.subtract(orderShipDtl.getShipNum());
                                orderShipDtl.setReturnCounts(orderShipDtl.getShipNum());
                                orderShipDtlService.updateById(orderShipDtl);
                            } else if (count.compareTo(orderShipDtl.getShipNum()) < 0) {
                                if (count.compareTo(BigDecimal.ZERO) != 0) {
                                    orderShipDtl.setReturnCounts(count);
                                    count = BigDecimal.ZERO;
                                    orderShipDtlService.updateById(orderShipDtl);
                                } else if (count.compareTo(BigDecimal.ZERO) == 0) {
                                    break;
                                }
                            }
                        }
                    }
                    orderItem.setPcwpReturn(orderItem.getPcwpReturn().subtract(pcwpCount));
                    if (byId.getProductType() != 12) {
                        ProductSku sku = productSkuService.getById(orderItem.getSkuId());
                        sku.setStock(sku.getStock().add(pcwpCount));
                        skuItemList.add(sku);
                    } else {
                        OrderSelectPlan orderPlan = orderSelectPlanService.getDataByorderItemId(orderItem.getOrderItemId());
                        MaterialMonthSupplyPlanDtl planDtl = materialMonthSupplyPlanDtlService.getById(orderPlan.getDtlId());
                        BigDecimal orderQty = planDtl.getOrderQty();
                        if (mallConfig.isCountPlanOrderNum == 1) {
                            // TODO 计划最新统计
                            BigDecimal qty = ordersService.getOrderUseCountBySelectPlanDtlIds(planDtl.getPlanDtlId());
                            orderQty = qty;
                            planDtl.setOrderQty(orderQty.add(pcwpCount));
                            materialMonthSupplyPlanDtlService.updateById(planDtl);
                        }
                    }
                    orderItems.add(orderItem);
                });
                if (byId.getOrderClass() != 1) {
                    ArrayList<OrderItem> sonOrderItemList = new ArrayList<>();
                    for (OrderItem orderItem : orderItems) {
                        OrderItem sonOrderItem = orderItemService.getOrderItemByParentId(orderItem.getOrderItemId());
                        if (sonOrderItem != null) {
                            sonOrderItem.setPcwpReturn(orderItem.getPcwpReturn());
                            sonOrderItemList.add(sonOrderItem);
                        }
                    }
                    List<String> collect = sonOrderItemList.stream().map(OrderItem::getOrderId).distinct().collect(Collectors.toList());
                    orderItemService.updateBatchById(sonOrderItemList);
                    ordersService.lambdaUpdate().eq(Orders::getOrderId, collect).set(Orders::getState, 8).update();
                }
                //修改订单
                ordersService.updateById(byId);
                if (byId.getProductType() != 12) {
                    productSkuService.updateBatchById(skuItemList);
                }
                orderItemService.updateBatchById(orderItems);
                //删除数据
                orderReturnService.removeById(orderReturn.getOrderReturnId());
                orderReturnItemService.lambdaUpdate().eq(OrderReturnItem::getOrderReturnId, orderReturn.getOrderReturnId()).remove();
                //修改订单项
            });
        }


//        StringBuilder stringBuilder = new StringBuilder();
//        InterfaceLogs interfaceLogs = interfaceLogsService.lambdaQuery().eq(InterfaceLogs::getSecretKey, keyId)
//                .eq(InterfaceLogs::getIsSuccess, 1)
//                .eq(InterfaceLogs::getLogType, 3)
//                .select()
//                .one();
//        if (interfaceLogs != null) {
//            String localArguments = interfaceLogs.getLocalArguments();
//            HashMap vo = JSON.parseObject(localArguments, HashMap.class);
//
//
//            //修该发货单数据
//            if (vo.get("shipDtl") != null) {
//                ArrayList<OrderReturnShipDtlVo> shipDtl = (ArrayList<OrderReturnShipDtlVo>) vo.get("shipDtl");
//                if (shipDtl != null && shipDtl.size() > 0) {
//                    ArrayList<OrderShipDtl> shipDtls = new ArrayList<>();
//                    for (OrderReturnShipDtlVo orderReturnShipDtlVo : shipDtl) {
//                        OrderShipDtl byId = orderShipDtlService.getById(orderReturnShipDtlVo.getDtlId());
//                        byId.setReturnCounts(orderReturnShipDtlVo.getReturnCounts());
//                        shipDtls.add(byId);
//                    }
////                    orderShipDtlService.updateBatchById(shipDtls);
//                }
//            }
//
//
//            //修改大宗集合数据
//            if (vo.get("planDtl") != null) {
//                ArrayList<HashMap<String, Object>> list = (ArrayList) vo.get("orderItemList");
//                if (list != null && list.size() > 0) {
//                    ArrayList<MaterialMonthSupplyPlanDtl> planDtls = new ArrayList<>();
//                    for (HashMap<String, Object> plan : list) {
//                        MaterialMonthSupplyPlanDtl byId = materialMonthSupplyPlanDtlService.getById(plan.get("id").toString());
//                        BigDecimal orderQty = byId.getOrderQty();
//                        if (mallConfig.isCountPlanOrderNum == 1) {
//                            // TODO 计划最新统计
//                            BigDecimal qty = ordersService.getOrderUseCountBySelectPlanDtlIds(byId.getPlanDtlId());
//                            orderQty = qty;
//                        }
//                        byId.setOrderQty(orderQty.add((BigDecimal) plan.get("returnCounts")));
//                        planDtls.add(byId);
//                    }
//                    materialMonthSupplyPlanDtlService.updateBatchById(planDtls);
//                }
//            }

//        }
    }

    @Override
    public void pushOrderShipData() {
//        StringBuilder stringBuilder = new StringBuilder();
//        List<OrderShip> list = lambdaQuery().eq(OrderShip::getType, 2)
//                .eq(OrderShip::getIsNotPush, 1).list();
//        for (OrderShip orderShip : list) {
//            Boolean r11Bool = null;
//            R r11 = null;
//            try {
//                r11 = restTemplateUtils.getPCWP2NotParams(mallConfig.prodPcwp2Url02
//                        + PCWP2ApiUtil.IS_CON_OPERA_BILL_URL + "?orgId=" + orderShip.getOrderId() + "&date=" + LocalDate.now().toString());
//            } catch (Exception e) {
//                throw new BusinessException("【远程异常】判断是否可推送验收单：" + e.getMessage());
//            }
//            if (r11.getCode() == null || r11.getCode() != 200) {
//                // 返回不是200异常
//                throw new BusinessException("【远程异常】判断是否可推送验收单：" + r11.getMessage());
//            } else {
//                r11Bool = (Boolean) r11.getData();
//                if (r11Bool != null && r11Bool == true) {
//                    outSupplierRecevieService.confirmShipUrl2(orderShip, orderShip.getIdStr(), stringBuilder);
//                    orderShip.setIsNotPush(0);
//                    update(orderShip);
//                }
//            }
//        }

    }


    @Override
    public void wxExportDataPurchase(String id, HttpServletResponse response) {
        OrderShipInfoVo orderShipInfoVo = baseMapper.exportDataByBillId(id);

        try {
            String src = mallConfig.templateFormUrl;
            Map<String, Object> dataMap = new HashMap<>();
            List<OrderShipDtl> dtls = orderShipInfoVo.getDtls();
            dataMap.put("shipData", DateUtil.getyyymmddHHmmss(orderShipInfoVo.getShipData()));
            dataMap.put("supplierName", orderShipInfoVo.getSupplierName());
            dataMap.put("receiveOrgName", orderShipInfoVo.getReceiveOrgName());
            dataMap.put("billSn", orderShipInfoVo.getBillSn());
            dataMap.put("orderSn", orderShipInfoVo.getOrderSn());
            dataMap.put("remarks", orderShipInfoVo.getRemarks());
            dataMap.put("totalPrice", orderShipInfoVo.getRateAmount());
            if (!orderShipInfoVo.getSupplierId().equals(orderShipInfoVo.getShipEnterpriseId())) {
                dataMap.put("remarks", "送货单位： " + orderShipInfoVo.getShipEnterpriseName());
            }
            for (OrderShipDtl dtl : dtls) {
                OrderItem byId = orderItemService.getById(dtl.getOrderItemId());
                if (byId != null) {
                    ProductCategory className = productCategoryService.getById(byId.getClassId());
                    if (className != null) {
                        dtl.setProductCategoryName(className.getClassName());
                    }
                }
            }
            dataMap.put("dataList", dtls);

            ExcelForWebUtil.exportExcel(response, dataMap, "微信物资入库单模板.xlsx", src, "入库单模板.xlsx");
        } catch (Exception e) {
            log.error("导出失败信息：" + e.getMessage());
            throw new BusinessException(500, "导出失败！");
        }

    }


    @Override
    public PageUtils WXqueryPage(JSONObject jsonObject, LambdaQueryWrapper<OrderShip> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        queryWrapper.eq(OrderShip::getSupplierId, user.getEnterpriseId()).or().eq(OrderShip::getShipEnterpriseId, user.getEnterpriseId());

        String abovePrice = (String) innerMap.get("abovePrice");
        String belowPrice = (String) innerMap.get("belowPrice");
        String orderId = (String) innerMap.get("orderId");
        Integer type = (Integer) innerMap.get("type");
        String orderSn = (String) innerMap.get("orderSn");
        Integer orderClass = (Integer) innerMap.get("orderClass");
        Integer sourceType = (Integer) innerMap.get("sourceType");
        String otherOrderSn = (String) innerMap.get("otherOrderSn");
        String staConfirmTime = (String) innerMap.get("staConfirmTime");
        String endConfirmTime = (String) innerMap.get("endConfirmTime");
        String startGmtCreate = (String) innerMap.get("startGmtCreate");
        String endGmtCreate = (String) innerMap.get("endGmtCreate");
        String staShipTime = (String) innerMap.get("staShipTime");
        String endShipTime = (String) innerMap.get("endShipTime");
        Integer orderBy = (Integer) innerMap.get("orderBy");
        String keywords = (String) innerMap.get("keywords");
        String receiveOrgName = (String) innerMap.get("receiveOrgName");
        if (orderId != null && orderId != "") {
            queryWrapper.eq(OrderShip::getOrderId, orderId);
        }
        if (belowPrice != null && belowPrice != "") {
            queryWrapper.lt(OrderShip::getRateAmount, belowPrice);
        }

        if (abovePrice != null && abovePrice != "") {
            queryWrapper.gt(OrderShip::getRateAmount, abovePrice);
        }
        if (type != null) {
            queryWrapper.eq(OrderShip::getType, type);
        }
        queryWrapper.eq(sourceType != null, OrderShip::getSourceType, sourceType);
        if (StringUtils.isNotBlank(orderSn)) {
            queryWrapper.eq(OrderShip::getOrderSn, orderSn);
        }
        if (StringUtils.isNotBlank(otherOrderSn)) {
            queryWrapper.eq(OrderShip::getOtherOrderSn, otherOrderSn);
        }
        if (staConfirmTime != null && staConfirmTime != "") {
            queryWrapper.gt(OrderShip::getConfirmTime, staConfirmTime);
        }
        if (endConfirmTime != null && endConfirmTime != "") {
            queryWrapper.lt(OrderShip::getConfirmTime, endConfirmTime);
        }
        if (startGmtCreate != null && startGmtCreate != "") {
            queryWrapper.gt(OrderShip::getGmtCreate, startGmtCreate);
        }
        if (endGmtCreate != null && endGmtCreate != "") {
            queryWrapper.lt(OrderShip::getGmtCreate, endGmtCreate);
        }
        if (staShipTime != null && staShipTime != "") {
            queryWrapper.gt(OrderShip::getShipData, staShipTime);
        }
        if (endShipTime != null && endShipTime != "") {
            queryWrapper.lt(OrderShip::getShipData, endShipTime);
        }
        if (orderBy != null) {
            if (orderBy == 1) {
                queryWrapper.orderByDesc(OrderShip::getGmtCreate);
            }
            if (orderBy == 2) {
                queryWrapper.orderByDesc(OrderShip::getFlishTime);
            }
            if (orderBy == 3) {
                queryWrapper.orderByDesc(OrderShip::getShipData);
            }
        } else {
            queryWrapper.orderByDesc(OrderShip::getGmtCreate);
        }
        if (receiveOrgName != null && receiveOrgName != "") {
            queryWrapper.like(OrderShip::getReceiveOrgName, receiveOrgName);
        }
        if (keywords != null && keywords != "") {
            queryWrapper.and(i -> i.like(OrderShip::getReceiveName, keywords)
                    .or().like(OrderShip::getReceiveOrgName, keywords)
                    .or().like(OrderShip::getBillSn, keywords)
                    .or().like(OrderShip::getOrderSn, keywords));
        }
        IPage<OrderShip> page = page(new Query<OrderShip>().getPage(jsonObject), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public void materialShipExport(String id, HttpServletResponse response) {
        OrderShip byId = getById(id);
        String src = mallConfig.templateFormUrl;
        Map<String, Object> dataMap = new HashMap<>();

        dataMap.put("supplierName", byId.getSupplierName());
        dataMap.put("shipEnterpriseId", byId.getShipEnterpriseName());
        dataMap.put("shipAddress", byId.getShipAddress());
        String yyyymmdd = DateUtil.getYYYYmmdd(byId.getConfirmTime(), "yyyy年 MM月 dd日");
        dataMap.put("confirmTime", yyyymmdd);
        List<MaterialShipDtlVo> dtls = orderShipDtlService.getMaterialShipDtlsByOrderShipId(id);
        dataMap.put("dataList", dtls);
        try {
            ExcelForWebUtil.exportExcel(response, dataMap, "实物收料单模板.xlsx", src, "实物收料单.xlsx");
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    @Override
    public void materialShipDzExport(String id, HttpServletResponse response) {
        OrderShip byId = getById(id);
        String src = mallConfig.templateFormUrl;
        Map<String, Object> dataMap = new HashMap<>();

        dataMap.put("supplierName", byId.getSupplierName());
        dataMap.put("shipEnterpriseId", byId.getShipEnterpriseName());
        dataMap.put("shipAddress", byId.getShipAddress());
        String yyyymmdd = DateUtil.getYYYYmmdd(byId.getConfirmTime(), "yyyy年 MM月 dd日");
        dataMap.put("confirmTime", yyyymmdd);
        List<MaterialShipDtlVo> dtls = orderShipDtlService.getMaterialShipDzDtlsByOrderShipId(id);
        dataMap.put("dataList", dtls);
        try {
            ExcelForWebUtil.exportExcel(response, dataMap, "实物收料单模板.xlsx", src, "实物收料单.xlsx");
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    @Override
    public void saveTwoOrderShip(OrderShipVo orderShipVo) {
        if (orderShipVo == null || orderShipVo.getDtls() == null || orderShipVo.getDtls().size() == 0) {
            throw new BusinessException(500, "请输入发货单或者填写发货单项");
        }
    }


    @Override
    public void updateDtls(OrderShip orderShip) {
        //发货单现在金额
        BigDecimal nowTotalAmoney = BigDecimal.valueOf(0.0);
        BigDecimal nowNoRateAmoney = BigDecimal.valueOf(0.0);
        BigDecimal nowTwoTotalAmoney = BigDecimal.valueOf(0.0);
        BigDecimal nowTwoNoRateAmoney = BigDecimal.valueOf(0.0);
        OrderShip byId = getById(orderShip.getBillId());
        if (byId.getType() == 2) {
            throw new BusinessException(500, "供应商已经收货，不能修改发货单");
        }
        ArrayList<OrderShipDtl> shipDtls = new ArrayList<>();
        List<OrderShipDtl> dtls = orderShip.getDtls();
        if (dtls == null || dtls.size() == 0) {
            throw new BusinessException(500, "请输入发货单明细");
        }
        for (OrderShipDtl dtl : dtls) {
            OrderShipDtl oldDtl = orderShipDtlService.getById(dtl.getDtlId());
            oldDtl.setShipNum(dtl.getShipCounts());
            OrderItem orderItem = orderItemService.getById(oldDtl.getOrderItemId());
            //查询商城退货数量
            BigDecimal returnCount = BigDecimal.valueOf(0);
            if (orderItem.getParentOrderItemId() != null) {
                returnCount = orderReturnItemService.getDataByOrderItmId(orderItem.getParentOrderItemId(), 0);
            } else {
                returnCount = orderReturnItemService.getDataByOrderItmId(orderItem.getOrderItemId(), 0);
            }
            //查询商城退货数量
            BigDecimal buyCounts = orderItem.getBuyCounts();
            BigDecimal shipCount = buyCounts.subtract(returnCount).add(oldDtl.getShipCounts()).add(orderItem.getPcwpReturn()).subtract(orderItem.getShipCounts());
            if (dtl.getShipCounts().compareTo(shipCount) <= 0) {
                //现在发货单项发货数量
                BigDecimal nowShipCount = orderItem.getShipCounts().subtract(oldDtl.getShipCounts()).add(dtl.getShipCounts());
                oldDtl.setShipCounts(dtl.getShipCounts());
                oldDtl.setShipNum(dtl.getShipCounts());
                orderItem.setShipCounts(nowShipCount);
                orderShipDtlService.updateAmount(orderItem, oldDtl, oldDtl.getShipCounts());
                //更新发货单金额
                nowTotalAmoney = nowTotalAmoney.add(oldDtl.getTotalAmount());
                //计算发货单的不含税金额
                nowNoRateAmoney = nowNoRateAmoney.add(oldDtl.getNoRateAmount());
                if (byId.getOtherOrderSn() != null) {
                    nowTwoTotalAmoney = nowTwoTotalAmoney.add(oldDtl.getOtherTotalAmount());
                    nowTwoNoRateAmoney = nowTwoNoRateAmoney.add(oldDtl.getNoRateAmount());
                }
                if (oldDtl.getOrderId() != null)
                    orderItemService.updateById(orderItem);
                shipDtls.add(oldDtl);
            } else {
                throw new BusinessException(500, "修改数量大于发货数量，不能修改");
            }


        }
        orderShipDtlService.updateBatchById(shipDtls);
        byId.setNoRateAmount(nowNoRateAmoney);
        byId.setRateAmount(nowTotalAmoney);
        if (byId.getOtherOrderSn() != null) {
            byId.setOtherRateAmount(nowTwoTotalAmoney);
            byId.setNoRateAmount(nowTwoNoRateAmoney);
        }
        update(byId);
//        lambdaQuery().get
//        if (dtls == null||dtls.size()==0){
//            throw new BusinessException(500, "请输入发货单信息");
//        }
//        List<OrderShipDtl> err = dtls.stream().filter(item -> item.getDtlId() == null || item.getShipCounts().compareTo(BigDecimal.ZERO) <= 0).collect(Collectors.toList());
//        if (err.size()>0){
//            throw new BusinessException(500, "请输入发货单信息");
//        }
    }


    @Override
    public void updateMonOrderShip(OrderShip orderShip) {
        OrderShip byId = getById(orderShip.getBillId());
        if (byId.getType() == 2) {
            throw new BusinessException(500, "供应商已经收货，不能修改发货单");
        }
        ArrayList<OrderShipDtl> shipDtls = new ArrayList<>();
        List<OrderShipDtl> dtls = orderShip.getDtls();
        if (dtls == null || dtls.size() == 0) {
            throw new BusinessException(500, "请输入发货单明细");
        }
        ArrayList<OrderShipmentsQtyIsOkDTO> list = new ArrayList<>();
        for (OrderShipDtl dtl : dtls) {
            //订单项
            OrderShipmentsQtyIsOkDTO orderShipmentsQtyIsOkDTO = new OrderShipmentsQtyIsOkDTO();

            OrderShipDtl oldDtl = orderShipDtlService.getById(dtl.getDtlId());
            //超量
            orderShipmentsQtyIsOkDTO.setOrderItemId(oldDtl.getOrderItemId());
            orderShipmentsQtyIsOkDTO.setDtlId(oldDtl.getDtlId());
            orderShipmentsQtyIsOkDTO.setUpdate(true);
            orderShipmentsQtyIsOkDTO.setQty(dtl.getShipCounts());
            list.add(orderShipmentsQtyIsOkDTO);


            oldDtl.setShipNum(dtl.getShipCounts());
            OrderItem orderItem = orderItemService.getById(oldDtl.getOrderItemId());
            //查询商城退货数量
            BigDecimal returnCount = orderReturnItemService.getDataByOrderItmId(orderItem.getOrderItemId(), 0);
            BigDecimal buyCounts = orderItem.getBuyCounts();
            buyCounts = buyCounts.add(buyCounts.multiply(BigDecimal.valueOf(0.1)));
            BigDecimal shipCount = buyCounts.subtract(returnCount).add(oldDtl.getShipCounts()).subtract(orderItem.getShipCounts());
            if (dtl.getShipCounts().compareTo(shipCount) <= 0) {
                BigDecimal nowShipCount = orderItem.getShipCounts().subtract(oldDtl.getShipCounts()).add(dtl.getShipCounts());
                orderItem.setShipCounts(nowShipCount);
                //现在发货单项发货数量
                oldDtl.setShipCounts(dtl.getShipCounts());
                oldDtl.setShipNum(dtl.getShipCounts());
                orderItemService.updateById(orderItem);
                if (byId.getOtherOrderSn() != null) {
                    orderItemService.lambdaUpdate().eq(OrderItem::getParentOrderItemId, oldDtl.getOrderItemId()).set(OrderItem::getShipCounts, nowShipCount).update();
                }
                shipDtls.add(oldDtl);
            } else {
                throw new BusinessException(500, "修改数量大于发货数量，不能修改");
            }


        }
//        orderItemService.orderShipmentsQtyIsOkLG(list);
        orderShipDtlService.updateBatchById(shipDtls);
        update(byId);
    }

    @Override
    public void UpdateDzOrderShip(OrderShip orderShip) {
        //发货单现在金额
        BigDecimal nowTotalAmoney = BigDecimal.valueOf(0.0);
        BigDecimal nowNoRateAmoney = BigDecimal.valueOf(0.0);
        BigDecimal nowTwoTotalAmoney = BigDecimal.valueOf(0.0);
        BigDecimal nowTwoNoRateAmoney = BigDecimal.valueOf(0.0);
        OrderShip byId = getById(orderShip.getBillId());
        if (byId.getType() == 2) {
            throw new BusinessException(500, "供应商已经收货，不能修改发货单");
        }
        ArrayList<OrderShipDtl> shipDtls = new ArrayList<>();
        List<OrderShipDtl> dtls = orderShip.getDtls();
        if (dtls == null || dtls.size() == 0) {
            throw new BusinessException(500, "请输入发货单明细");
        }
        ArrayList<OrderShipmentsQtyIsOkDTO> list = new ArrayList<>();
        for (OrderShipDtl dtl : dtls) {
            //订单项
            OrderShipmentsQtyIsOkDTO orderShipmentsQtyIsOkDTO = new OrderShipmentsQtyIsOkDTO();

            OrderShipDtl oldDtl = orderShipDtlService.getById(dtl.getDtlId());
            //超量
            orderShipmentsQtyIsOkDTO.setOrderItemId(oldDtl.getOrderItemId());
            orderShipmentsQtyIsOkDTO.setDtlId(oldDtl.getDtlId());
            orderShipmentsQtyIsOkDTO.setUpdate(true);
            orderShipmentsQtyIsOkDTO.setQty(dtl.getShipCounts());
            list.add(orderShipmentsQtyIsOkDTO);


            oldDtl.setShipNum(dtl.getShipCounts());
            OrderItem orderItem = orderItemService.getById(oldDtl.getOrderItemId());
            //查询商城退货数量
            BigDecimal returnCount = orderReturnItemService.getDataByOrderItmId(orderItem.getOrderItemId(), 0);
            BigDecimal buyCounts = orderItem.getBuyCounts();
            buyCounts = buyCounts.add(buyCounts.multiply(BigDecimal.valueOf(0.1)));
            BigDecimal shipCount = buyCounts.subtract(returnCount).add(oldDtl.getShipCounts()).subtract(orderItem.getShipCounts());
            if (dtl.getShipCounts().compareTo(shipCount) <= 0) {
                BigDecimal nowShipCount = orderItem.getShipCounts().subtract(oldDtl.getShipCounts()).add(dtl.getShipCounts());
                orderItem.setShipCounts(nowShipCount);
                //现在发货单项发货数量
                //现在发货单项发货数量
                oldDtl.setShipCounts(dtl.getShipCounts());
                oldDtl.setShipNum(dtl.getShipCounts());
                orderShipDtlService.updateAmount(orderItem, oldDtl, oldDtl.getShipCounts());
                nowTotalAmoney = nowTotalAmoney.add(oldDtl.getTotalAmount());
                nowNoRateAmoney = nowNoRateAmoney.add(oldDtl.getNoRateAmount());
                if (byId.getOtherOrderSn() != null) {
                    nowTwoTotalAmoney = nowTwoTotalAmoney.add(oldDtl.getOtherTotalAmount());
                    nowTwoNoRateAmoney = nowTwoNoRateAmoney.add(oldDtl.getNoRateAmount());
                }
                orderItemService.updateById(orderItem);
                shipDtls.add(oldDtl);
            } else {
                throw new BusinessException(500, "修改数量大于发货数量，不能修改");
            }


        }
        orderItemService.orderShipmentsQtyIsOkLG(list);
        orderShipDtlService.updateBatchById(shipDtls);
        byId.setNoRateAmount(nowNoRateAmoney);
        byId.setRateAmount(nowTotalAmoney);
        if (byId.getOtherOrderSn() != null) {
            byId.setOtherRateAmount(nowTwoTotalAmoney);
            byId.setNoRateAmount(nowTwoNoRateAmoney);
        }
        update(byId);

    }

    @Override
    public void shippingOrderShip(OrderShip one, User user) {
        String deliveryFlowId = one.getDeliveryFlowId();
        if (deliveryFlowId == null || deliveryFlowId == "") {
            throw new BusinessException(500, "该用户没有填写发货单号");
        }
        //物流公司
        String company = one.getLogisticsCompany();
        if (company == null || company == "") {
            throw new BusinessException(1002, "该用户没有填写物流公司");
        }
        OrderShip info = lambdaQuery().eq(OrderShip::getBillId, one.getBillId()).one();
        //判断发货单是否已经发货
        if (info.getType() == 1) {
            throw new BusinessException(302, "该发货单已经发货");
        }


//        if (!info.getShipEnterpriseId().equals(user.getEnterpriseId())) {
//            throw new BusinessException(500, "该用户没有发货权限");
//        }
        if (info != null) {
            if (info.getType() == 0) {
                info.setDeliveryFlowId(one.getDeliveryFlowId());
                info.setLogisticsCompany(one.getLogisticsCompany());
                info.setType(1);
                info.setShipData(new Date());
                info.setShipUserId(user.getUserId());
                update(info);
                /***
                 *  查看订单项是否发货完,修改订单状态
                 */
                orderItemService.updateOrderStateAndOrderItemByShip(info);

            } else {
                throw new BusinessException(302, "该发货单已经发货");
            }

        } else {
            throw new BusinessException(302, "该发货单不存在");
        }

    }

    @Override
    public void delMonthOrderShipByBillSn(OrderShip byId) {
        if (byId != null) {
            if (byId.getType() == 0) {
                List<OrderShipDtl> dtls = orderShipDtlService.getDataByOrderShipId(byId.getBillId());
                for (OrderShipDtl dtl : dtls) {
                    OrderItem orderItem = orderItemService.getById(dtl.getOrderItemId());
                    orderItem.setShipCounts(orderItem.getShipCounts().subtract(dtl.getShipCounts()));
//                    if (orderItem.getParentOrderItemId()!=null){
//                        OrderItem parent = orderItemService.getById(orderItem.getParentOrderItemId());
//                        parent.setShipCounts( orderItem.getShipCounts());
//                        orderItemService.updateShipCounts(parent);
//                    }

                    orderShipDtlService.removeById(dtl.getDtlId());
                    orderItemService.updateShipCounts(orderItem);
                }
                super.removeById(byId.getBillId());

            } else {
                throw new BusinessException(603, "发货单已确认，不能删除");
            }
        } else {
            throw new BusinessException(602, "发货单不存在");
        }
    }

    @Override
    public void delDzOrderShipByBillSn(OrderShip byId) {
        if (byId.getType() == 0) {
            List<OrderShipDtl> dtls = orderShipDtlService.getDataByOrderShipId(byId.getBillId());
            for (OrderShipDtl dtl : dtls) {
                OrderItem orderItem = orderItemService.getById(dtl.getOrderItemId());
                orderItem.setShipCounts(orderItem.getShipCounts().subtract(dtl.getShipCounts()));
//                    if (orderItem.getParentOrderItemId()!=null){
//                        OrderItem parent = orderItemService.getById(orderItem.getParentOrderItemId());
//                        parent.setShipCounts( orderItem.getShipCounts());
//                        orderItemService.updateShipCounts(parent);
//                    }

                orderShipDtlService.removeById(dtl.getDtlId());
                orderItemService.updateShipCounts(orderItem);
            }
            super.removeById(byId.getBillId());

        } else {
            throw new BusinessException(603, "发货单已确认，不能删除");
        }
    }

    @Override
    public void delLiXinBillId(OrderShip byId) {

        if (byId.getType() == 0) {
            List<OrderShipDtl> dtls = orderShipDtlService.getDataByOrderShipId(byId.getBillId());
            for (OrderShipDtl dtl : dtls) {
                OrderItem orderItem = orderItemService.getById(dtl.getOrderItemId());
                orderItem.setShipCounts(orderItem.getShipCounts().subtract(dtl.getShipCounts()));
//                    if (orderItem.getParentOrderItemId()!=null){
//                        OrderItem parent = orderItemService.getById(orderItem.getParentOrderItemId());
//                        parent.setShipCounts( orderItem.getShipCounts());
//                        orderItemService.updateShipCounts(parent);
//                    }

                orderShipDtlService.removeById(dtl.getDtlId());
                orderItemService.updateShipCounts(orderItem);
            }
            super.removeById(byId.getBillId());

        } else {
            throw new BusinessException(603, "发货单已确认，不能删除");
        }
    }


    @Override
    public void OutSaveOrderShip(OrderShipVo orderShipVo, User user) {
        if (orderShipVo == null || orderShipVo.getDtls() == null || orderShipVo.getDtls().size() == 0) {
            throw new BusinessException(500, "请输入发货单或者填写发货单项");
        }
        OrderShip orderShip = new OrderShip();
        orderShipVo.setShipUserId(user.getUserId());
        orderShipVo.setShipUserName(user.getNickName());
        orderShipVo.setShipUserName(user.getNickName());
        Orders orders = ordersService.getById(orderShipVo.getOrderId());
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, orders.getSupplierId()).one();
        if (orders.getParentOrderId() != null) {
            Orders one = ordersService.getById(orders.getParentOrderId());
            orderShipVo.setOrderClass(3);
            orderShipVo.setShipEnterpriseName(one.getSupplierName());
            orderShipVo.setOrderId(one.getOrderId());
            orderShipVo.setOrderSn(one.getOrderSn());
            orderShipVo.setOtherOrderId(orders.getOrderId());
            orderShipVo.setOtherOrderSn(orders.getOrderSn());
            orderShipVo.setShipEnterpriseId(orders.getSupplierId());
            orderShipVo.setShipEnterpriseName(orders.getSupplierName());
            orderShip = outSaveShipInfo(orderShipVo, one);
        } else {
            orderShip = outSaveShipInfo(orderShipVo, orders);
        }
        //外部接口，没有登录，
        orderShip.setSupplierCode(enterpriseInfo.getShortCode());
        orderShip.setFounderId(user.getUserId());
        orderShip.setFounderName(user.getNickName());
        orderShip.setShipEnterpriseName(orders.getSupplierName());
        orderShip.setShipEnterpriseId(orders.getSupplierId());

        BigDecimal rateAmous = BigDecimal.valueOf(0);
        BigDecimal noRateAmous = BigDecimal.valueOf(0);
        BigDecimal otherRateAmous = BigDecimal.valueOf(0);
        BigDecimal otherNoRateAmous = BigDecimal.valueOf(0);
        List<OrderShipDtl> dtls = orderShip.getDtls();
        //判断传递的发货单项的数量是否全部为0
        int dtlcounts = 0;
        //根据订单项和来源创建发货单明细
        ArrayList<OrderShipmentsQtyIsOkDTO> list = new ArrayList<>();
        for (OrderShipDtl dtl : dtls) {
            dtl.setFounderId(user.getUserId());
            dtl.setFounderName(user.getNickName());
//            dtl.setShipNum(dtl.getShipCounts());
            if (dtl.getShipCounts().compareTo(BigDecimal.valueOf(0)) == 0) {
                dtlcounts++;
                continue;
            } else {
                dtl.setBillId(orderShip.getBillId());
                dtl.setBillSn(orderShip.getBillSn());
                dtl.setOrderId(orderShip.getOrderId());
                dtl.setOrderSn(orderShip.getOrderSn());

                //根据订单项生成发货单项，并修改订单发货数量（原有数量+本次发货数量）
                createOrderShipDtl(dtl, orderShip.getSourceType(), list);
                rateAmous = rateAmous.add(dtl.getTotalAmount());
                noRateAmous = noRateAmous.add(dtl.getNoRateAmount());
                if (orders.getOrderClass() != 1) {
                    otherRateAmous = otherRateAmous.add(dtl.getOtherTotalAmount());
                    otherNoRateAmous = otherNoRateAmous.add(dtl.getOtherNoRateAmount());
                }
                if (orderShip.getSourceType() == 1) {
                    orderItemService.orderShipmentsQtyIsOkYG(list);
                } else if (orderShip.getSourceType() == 6) {
                    orderItemService.orderShipmentsQtyIsOkLG(list);
                }

            }
        }
        if (dtls.size() == dtlcounts) {
            throw new BusinessException(500, "请填写发货数量");
        }
        orderShip.setRateAmount(rateAmous);
        orderShip.setNoRateAmount(noRateAmous);
        orderShip.setOtherRateAmount(otherRateAmous);
        orderShip.setOtherNoRateAmount(otherNoRateAmous);
        update(orderShip);
        orderShipDtlService.saveBatch(dtls);

//        orderShip.setSupplierOrgId(user.getOrgId());
//        orderShip.setSupplierCode(user.getSocialCreditCode());
    }

    @Override
    public void remindAudit(String enterpriseId) {
        //判断当前用户是否有未提交的审核
        StationMessageReceiveVO vo = new StationMessageReceiveVO();
        vo.setTitle("收料提交审核通知");
        vo.setContent("您好，请去PCWP办理现场收料提交审核");
        ArrayList<String> id = new ArrayList<>(1);
        id.add(enterpriseId);
        vo.setEnterpriseIdList(id);
        stationMessageService.createBatch(vo);
    }
    public void remindCompleted(String orderId,UserLogin user) {
        StationMessageReceiveVO vo = new StationMessageReceiveVO();
        vo.setTitle("订单收料完成提醒");
        if( 0 == user.getIsInterior()){
            vo.setContent("尊敬的用户:您好!您的 "+ orderId+" 订单已经由收料人员 "+user.getUserName()+" 收料完成，请前往pcwp平台查看具体详情！");
        }else{
            vo.setContent("尊敬的用户:您好!您的 "+ orderId+" 订单已经由收料人员 "+user.getUserName()+" 收料完成，请点击<a href='https://example.com'>查看详情</a>查看具体详情！");
        }
        ArrayList<String> id = new ArrayList<>(1);
        id.add(user.getEnterpriseId());
        vo.setEnterpriseIdList(id);
        stationMessageService.createBatch(vo);
    }
}



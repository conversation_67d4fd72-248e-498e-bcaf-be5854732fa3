package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import scrbg.meplat.mall.common.constant.ProcessConstants;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
import scrbg.meplat.mall.enums.file.FileEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.EnterpriseInfoMapper;
import scrbg.meplat.mall.mapper.ShopSupplierReleMapper;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.util.pageUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @描述：店铺供方关联表 服务类
 * @作者: ye
 * @日期: 2023-06-05
 */
@Service
public class ShopSupplierReleServiceImpl extends ServiceImpl<ShopSupplierReleMapper, ShopSupplierRele> implements ShopSupplierReleService {
    @Autowired
    EnterpriseInfoService enterpriseInfoService;
    @Autowired
    EnterpriseInfoMapper enterpriseInfoMapper;
    @Autowired
    ShopService shopService;

    @Autowired
    ProductService productService;
    @Autowired
    ShopBusinessService shopBusinessService;

    @Autowired
    StringRedisTemplate stringRedisTemplate;
    @Autowired
    ShoppingCartService shoppingCartService;

    @Autowired
    FileService fileService;

    @Autowired
    ProcessConfigService processConfigService;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ShopSupplierRele> queryWrapper) {
        String keywords = (String) jsonObject.get("keywords");
        queryWrapper.like(StringUtils.isNotBlank(keywords), ShopSupplierRele::getSupplierName, keywords);
        IPage<ShopSupplierRele> page = this.page(
                new Query<ShopSupplierRele>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    @Transactional
    public void create(ShopSupplierRele shopSupplierRele) {
        if (shopSupplierRele.getSupplierId() != null) {
            UserLogin user = ThreadLocalUtil.getCurrentUser();
            EnterpriseInfo enterpriseInfo = enterpriseInfoService.getById(shopSupplierRele.getSupplierId());
            if (enterpriseInfo != null) {
                shopSupplierRele.setSupplierName(enterpriseInfo.getEnterpriseName());
                shopSupplierRele.setShopId(user.getShopId());
                if (shopSupplierRele.getTaxRate() != null) {
                    if (shopSupplierRele.getTaxRate().compareTo(new BigDecimal(0)) <= 0 || shopSupplierRele.getTaxRate().compareTo(new BigDecimal(100)) > 0) {
                        throw new BusinessException("税率超出限制");
                    }
                    boolean update = enterpriseInfoService.lambdaUpdate()
                            .eq(EnterpriseInfo::getEnterpriseId, enterpriseInfo.getEnterpriseId())
                            .set(EnterpriseInfo::getTaxRate, shopSupplierRele.getTaxRate())
                            .update();
                    if (!update) {
                        throw new BusinessException("企业税率更新失败");
                    }

                }

                //调用父类方法即可
                //也可以baseMapper.insert
                super.save(shopSupplierRele);
            } else {
                throw new BusinessException(1002, "企业不存在或者企业被禁用");
            }

        } else {
            throw new BusinessException(1003, "请输入供应商名称");
        }

    }

    @Override
    @Transactional
    public void update(ShopSupplierRele shopSupplierRele) {
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.getById(shopSupplierRele.getSupplierId());
        if (shopSupplierRele.getTaxRate() != null) {
            if (shopSupplierRele.getTaxRate().compareTo(new BigDecimal(0)) <= 0 || shopSupplierRele.getTaxRate().compareTo(new BigDecimal(100)) > 0) {
                throw new BusinessException("税率超出限制");
            }
            boolean update = enterpriseInfoService.lambdaUpdate()
                    .eq(EnterpriseInfo::getEnterpriseId, enterpriseInfo.getEnterpriseId())
                    .set(EnterpriseInfo::getTaxRate, shopSupplierRele.getTaxRate())
                    .update();
            if (!update) {
                throw new BusinessException("企业税率更新失败");
            }

        }
        super.updateById(shopSupplierRele);
    }

//    @Override
//    public void update (ShopSupplierRele shopSupplierRele) {
//        List<String> listPermissionsTypeList = shopSupplierRele.getlistPermissionsTypelist();
//        if (listPermissionsTypeList!=null&&listPermissionsTypeList.size()>0){
//            String s = JSON.toSONString(listPermissionsTypeList);
//        }shopSupplierRele.setListPermissionsType(s);
//        super.updateById(shopSupplierRele);}

    @Override
    public ShopSupplierRele getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        ShopSupplierRele byId = getById(id);
        List<Product> list = productService.lambdaQuery().eq(Product::getShopId, byId.getShopId()).eq(Product::getSupperBy, byId.getSupplierId()).list();
        //二级供应商移除，二级供应商的商品状态变为2(下架)，同时删除购物车中的商品
        if (!CollectionUtils.isEmpty(list)) {
            List<String> collect = list.stream().map(Product::getProductId).collect(Collectors.toList());
            productService.lambdaUpdate().in(Product::getProductId, collect).set(Product::getState, 2).update();
            shoppingCartService.lambdaUpdate().in(ShoppingCart::getProductId, collect).set(MustBaseEntity::getIsDelete, -1).update();
        }

        super.removeById(id);
    }

    /**
     * 根据店铺查询对应关联的供应商
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils listByShopId(JSONObject jsonObject, LambdaQueryWrapper<ShopSupplierRele> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
//         Integer productType = (Integer) innerMap.get("productType");
        String keywords = (String) jsonObject.get("keywords");
        String supplierType = (String) jsonObject.get("supplierType");
        Boolean templateWarehousedIs = (Boolean) jsonObject.get("templateWarehousedIs");
        String supplierGroup = (String) jsonObject.get("supplierGroup");
        q.like(StringUtils.isNotBlank(keywords), ShopSupplierRele::getSupplierName, keywords);
        q.eq(StringUtils.isNotBlank(supplierType), ShopSupplierRele::getSupplierType, supplierType);
        q.eq(Objects.nonNull(templateWarehousedIs), ShopSupplierRele::getTemplateWarehousedIs, templateWarehousedIs);
        q.eq(StringUtils.isNotBlank(supplierGroup), ShopSupplierRele::getSupplierGroup, supplierGroup);
        String shopId = ThreadLocalUtil.getCurrentUser().getShopId();
        q.eq(ShopSupplierRele::getShopId, shopId);
        q.orderByAsc(ShopSupplierRele::getSort, ShopSupplierRele::getGmtCreate);
        IPage<ShopSupplierRele> page = this.page(new Query<ShopSupplierRele>().getPage(jsonObject), q);
        // 查询返回供应商的税率数据
        List<String> supplierList = page.getRecords().stream().map(ShopSupplierRele::getSupplierId).collect(Collectors.toList());
        //enterpriseInfoService.lambdaQuery().
        if (!org.springframework.util.CollectionUtils.isEmpty(supplierList)) {
            List<EnterpriseInfo> enterpriseInfos = enterpriseInfoMapper.selectBatchIds(supplierList);
            for (ShopSupplierRele record : page.getRecords()) {
                for (EnterpriseInfo info : enterpriseInfos) {
                    if (info.getEnterpriseId().equals(record.getSupplierId())) {
                        if (info.getTaxRate() != null) {
                            record.setTaxRate(info.getTaxRate());
                        }
                    }
                }
            }
        }
        return new PageUtils(page);
    }

    @Override
    public List<String> getSupplierIdByShopId(String shopId) {
        LambdaQueryWrapper<ShopSupplierRele> q = new LambdaQueryWrapper<>();
        q.eq(ShopSupplierRele::getShopId, shopId)
                .select(ShopSupplierRele::getSupplierId);
        List<ShopSupplierRele> list = list(q);
        ArrayList<String> supplierList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(list)) {
            list.stream().forEach(item -> supplierList.add(item.getSupplierId()));
        }

        return supplierList;
    }

    /**
     * 获取本机构下供店铺
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils listShopListBySupplierId(JSONObject jsonObject, LambdaQueryWrapper<ShopSupplierRele> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        innerMap.put("enterpriseId", ThreadLocalUtil.getCurrentUser().getEnterpriseId());
        List<String> shopIds = shopBusinessService.getStopBusiness();
        if (shopIds != null && shopIds.size() > 0) {
            innerMap.put("shopIds", shopIds);
        }
        //分页
        int count = baseMapper.listShopListBySupplierIdCount(innerMap);
        pageUtils.pageDispose(jsonObject, count);
        Page<ShopSupplierRele> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        List<ShopSupplierRele> vos = baseMapper.listShopListBySupplierId(pages, innerMap);
        pages.setRecords(vos);
        return new PageUtils(pages);
    }

    /**
     * 获取本机构下供店铺
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils listShopList(JSONObject jsonObject, LambdaQueryWrapper<ShopSupplierRele> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
//        innerMap.put("enterpriseId",ThreadLocalUtil.getCurrentUser().getEnterpriseId());
//        List<String> shopIds = shopBusinessService.getStopBusiness();
//        if (shopIds!=null&&shopIds.size()>0){
//            innerMap.put("shopIds",shopIds);
//        }
        //分页
        int count = baseMapper.listShopListCount(innerMap);
        pageUtils.pageDispose(jsonObject, count);
        Page<ShopSupplierRele> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        List<ShopSupplierRele> vos = baseMapper.listShopList(pages, innerMap);
        pages.setRecords(vos);
        return new PageUtils(pages);
    }

    /**
     * 判断是否子供应商
     *
     * @return
     */
    @Override
    public ShopSupplierRele isTwoSupper() {
        return lambdaQuery().eq(ShopSupplierRele::getSupplierId, ThreadLocalUtil.getCurrentUser().getEnterpriseId()).last("limit 1").one();
    }

    @Override
    public PageUtils listBySupplierId(JSONObject jsonObject, LambdaQueryWrapper<ShopSupplierRele> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
//         Integer productType = (Integer) innerMap.get("productType");
        String keywords = (String) jsonObject.get("keywords");
        String supplierId = (String) jsonObject.get("supplierId");
        String shipId = shopService.getDataByEnterPriseId(supplierId).getShopId();

        q.like(StringUtils.isNotBlank(keywords), ShopSupplierRele::getSupplierName, keywords);

        q.orderByAsc(ShopSupplierRele::getSort, ShopSupplierRele::getGmtCreate);
        IPage<ShopSupplierRele> page = this.page(
                new Query<ShopSupplierRele>().getPage(jsonObject),
                q
        );
        // 查询返回供应商的税率数据
        List<String> supplierList = page.getRecords().stream().map(ShopSupplierRele::getSupplierId).collect(Collectors.toList());
        //enterpriseInfoService.lambdaQuery().
        if (!org.springframework.util.CollectionUtils.isEmpty(supplierList)) {
            List<EnterpriseInfo> enterpriseInfos = enterpriseInfoMapper.selectBatchIds(supplierList);
            for (ShopSupplierRele record : page.getRecords()) {
                for (EnterpriseInfo info : enterpriseInfos) {
                    if (info.getEnterpriseId().equals(record.getSupplierId())) {
                        if (info.getTaxRate() != null) {
                            record.setTaxRate(info.getTaxRate());
                        }
                    }
                }
            }
        }
        return new PageUtils(page);
    }

    @Override
    public PageUtils querySuppliersByOrderConditions(JSONObject jsonObject) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();

        // 参数处理
        String shopId = (String) jsonObject.get("shopId");
        String startDate = (String) jsonObject.get("startDate");
        String endDate = (String) jsonObject.get("endDate");
        Object productTypeObj = jsonObject.get("productType");
        Integer billType = (Integer) jsonObject.get("billType");
        String keywords = (String) jsonObject.get("keywords");

        // 处理productType参数，支持字符串和整数类型
        List<String> productTypeList = null;
        if (productTypeObj != null) {
            String productTypeStr = productTypeObj.toString();
            // 根据不同值构建对应的列表
            if ("0".equals(productTypeStr)) {
                productTypeList = Arrays.asList("0", "10");
            } else if ("1".equals(productTypeStr)) {
                productTypeList = Arrays.asList("1", "13");
            } else if ("2".equals(productTypeStr)) {
                productTypeList = Arrays.asList("2");
            } else {
                // 处理其他未定义的情况，可根据需求调整（例如设为空列表或保持原分割逻辑）
                productTypeList = Collections.emptyList();
            }
        }
        // 设置查询条件
        innerMap.put("shopId", shopId);
        innerMap.put("startDate", startDate);
        innerMap.put("endDate", endDate);
        innerMap.put("productTypeList", productTypeList);
        innerMap.put("billType", billType);
        innerMap.put("keywords", keywords);

        // 分页处理
        int count = baseMapper.querySuppliersByOrderConditionsCount(innerMap);
        pageUtils.pageDispose(jsonObject, count);

        // 安全获取分页参数
        Integer page = getIntegerValue(jsonObject.get("page"), 1);
        Integer limit = getIntegerValue(jsonObject.get("limit"), 10);

        Page<ShopSupplierRele> pages = new Page<>(page, limit);
        List<ShopSupplierRele> vos = baseMapper.querySuppliersByOrderConditions(pages, innerMap);
        pages.setRecords(vos);
        return new PageUtils(pages);
    }

    /**
     * 安全获取整数值
     *
     * @param value        原始值
     * @param defaultValue 默认值
     * @return 整数值
     */
    private Integer getIntegerValue(Object value, Integer defaultValue) {
        if (value == null) {
            return defaultValue;
        }
        if (value instanceof Integer) {
            return (Integer) value;
        }
        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return defaultValue;
            }
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return defaultValue;
    }

    @Override
    public void setSupplierType(ShopSupplierRele shopSupplierRele) {
        if (null == shopSupplierRele) {
            throw new BusinessException("数据不存在");
        }
        List<String> ids = shopSupplierRele.getIds();
        if (null == ids || ids.isEmpty()) {
            throw new BusinessException("未选择需要设置的供应商");
        }
        ids.forEach(id -> {
            ShopSupplierRele relation = new ShopSupplierRele();
            relation.setShopSupplierReleId(id);
            relation.setSupplierType(shopSupplierRele.getSupplierType());
            relation.setTemplateWarehousedIs(shopSupplierRele.getTemplateWarehousedIs());
            relation.setWarehousedType(shopSupplierRele.getWarehousedType());
            relation.setWarehousedDesc(shopSupplierRele.getWarehousedDesc());
            relation.setWarehousedFile(shopSupplierRele.getWarehousedFile());
            relation.setAuditStatus(1);
            super.updateById(relation);
            if (null != shopSupplierRele.getFiles() && !shopSupplierRele.getFiles().isEmpty()) {
                List<File> files = shopSupplierRele.getFiles();
                for (File file : files) {
                    file.setRelevanceId(id);
                    if (file.getRelevanceType() == null) {
                        file.setRelevanceType(17);
                    }
                }
                boolean b = fileService.saveBatch(files);
                if (!b) {
                    throw new BusinessException(400, "附件资料保存失败！");
                }
            }
            processConfigService.myFunc(ProcessConstants.UPSTREAM_SUPPLIER_ADMIT,
                    ThreadLocalUtil.getCurrentUser(), 0,id,"");
        });
    }

    @Override
    public void setSupplierGroup(ShopSupplierRele shopSupplierRele) {
        if (null == shopSupplierRele) {
            throw new BusinessException("数据不存在");
        }
        if (StringUtils.isBlank(shopSupplierRele.getSupplierGroup())) {
            throw new BusinessException("未选择需要设置的群组");
        }
        List<String> ids = shopSupplierRele.getIds();
        if (null == ids || ids.isEmpty()) {
            throw new BusinessException("未选择需要设置的供应商");
        }
        ids.forEach(id -> {
            ShopSupplierRele oldShopSupplierRele = getById(id);
            ShopSupplierRele relation = new ShopSupplierRele();
            relation.setShopSupplierReleId(id);
            if (StringUtils.isNotBlank(oldShopSupplierRele.getSupplierGroup())) {
                relation.setSupplierGroup(Stream.of(oldShopSupplierRele.getSupplierGroup(),
                                shopSupplierRele.getSupplierGroup())
                        .flatMap(str -> Arrays.stream(str.split(",")))
                        .distinct().collect(Collectors.joining(",")));
            } else {
                relation.setSupplierGroup(shopSupplierRele.getSupplierGroup());
            }
            super.updateById(relation);
        });

    }

    @Override
    public List<File> querySupplierFile(String id) {
        return fileService.listFileByParameters(
                id, FileEnum.RELEVANCE_TYPE_SUPPLIER.getCode(), FileEnum.IS_MIN_NO.getCode(), FileEnum.TYPE_ACCESSORY.getCode(), FileEnum.IMG_TYPE_GENERAL.getCode()
        );
    }

    @Override
    public void changeStatus(String id, int state) {
        ShopSupplierRele shopSupplierRele = new ShopSupplierRele();
        shopSupplierRele.setSupplierId(id);
        shopSupplierRele.setAuditStatus(state);
        updateById(shopSupplierRele);
        processConfigService.myFunc(ProcessConstants.UPSTREAM_SUPPLIER_ADMIT,
                ThreadLocalUtil.getCurrentUser(), --state,id,"");
    }
}